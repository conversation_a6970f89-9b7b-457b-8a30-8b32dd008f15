export interface User {
  id: string;
  email: string;
  password_hash: string;
  name: string;
  avatar_url?: string;
  phone?: string;
  status: UserStatus;
  email_verified: boolean;
  last_login_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface CreateUserRequest {
  email: string;
  password: string;
  name: string;
  phone?: string;
}

export interface UpdateUserRequest {
  name?: string;
  phone?: string;
  avatar_url?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
  remember_me?: boolean;
}

export interface LoginResponse {
  user: PublicUser;
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export interface PublicUser {
  id: string;
  email: string;
  name: string;
  avatar_url?: string;
  phone?: string;
  status: UserStatus;
  email_verified: boolean;
  last_login_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface Organization {
  id: string;
  name: string;
  domain?: string;
  logo_url?: string;
  settings: Record<string, any>;
  subscription_plan: SubscriptionPlan;
  subscription_expires_at?: Date;
  status: OrganizationStatus;
  created_at: Date;
  updated_at: Date;
}

export interface UserOrganization {
  id: string;
  user_id: string;
  organization_id: string;
  role: OrganizationRole;
  permissions: Record<string, any>;
  status: UserOrganizationStatus;
  joined_at: Date;
}

export interface CreateOrganizationRequest {
  name: string;
  domain?: string;
  logo_url?: string;
}

export interface UpdateOrganizationRequest {
  name?: string;
  domain?: string;
  logo_url?: string;
  settings?: Record<string, any>;
}

export interface InviteUserRequest {
  email: string;
  role: OrganizationRole;
  permissions?: Record<string, any>;
}

export interface UserSession {
  id: string;
  user_id: string;
  token_hash: string;
  refresh_token_hash?: string;
  expires_at: Date;
  ip_address?: string;
  user_agent?: string;
  created_at: Date;
  last_used_at: Date;
}

export interface AuditLog {
  id: string;
  user_id?: string;
  organization_id?: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  details: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  created_at: Date;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirm {
  token: string;
  new_password: string;
}

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
}

export interface EmailVerificationRequest {
  token: string;
}

export interface MFASetupResponse {
  secret: string;
  qr_code: string;
  backup_codes: string[];
}

export interface MFAVerifyRequest {
  token: string;
  code: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

export enum OrganizationStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

export enum UserOrganizationStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive'
}

export enum OrganizationRole {
  OWNER = 'owner',
  ADMIN = 'admin',
  MANAGER = 'manager',
  MEMBER = 'member',
  VIEWER = 'viewer'
}

export enum SubscriptionPlan {
  BASIC = 'basic',
  PROFESSIONAL = 'professional',
  ENTERPRISE = 'enterprise'
}

export enum AuditAction {
  USER_LOGIN = 'user.login',
  USER_LOGOUT = 'user.logout',
  USER_REGISTER = 'user.register',
  USER_UPDATE = 'user.update',
  USER_DELETE = 'user.delete',
  PASSWORD_CHANGE = 'password.change',
  PASSWORD_RESET = 'password.reset',
  EMAIL_VERIFY = 'email.verify',
  MFA_ENABLE = 'mfa.enable',
  MFA_DISABLE = 'mfa.disable',
  ORGANIZATION_CREATE = 'organization.create',
  ORGANIZATION_UPDATE = 'organization.update',
  ORGANIZATION_DELETE = 'organization.delete',
  USER_INVITE = 'user.invite',
  USER_REMOVE = 'user.remove',
  ROLE_CHANGE = 'role.change'
}

export interface JWTPayload {
  sub: string; // user id
  email: string;
  name: string;
  org_id?: string;
  role?: OrganizationRole;
  iat: number;
  exp: number;
  iss: string;
  aud: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Record<string, string[]>;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}
