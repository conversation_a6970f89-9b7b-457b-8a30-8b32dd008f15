{"name": "docmind-document-service", "version": "1.0.0", "description": "DocMind Document Service - 文档管理和存储服务", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "migrate": "npm run build && node dist/database/migrate.js", "seed": "npm run build && node dist/database/seed.js", "docker:build": "docker build -t docmind-document-service .", "docker:run": "docker run -p 3002:3000 docmind-document-service"}, "keywords": ["document", "file-management", "storage", "version-control", "upload"], "author": "DocMind Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "joi": "^17.11.0", "pg": "^8.11.3", "mongodb": "^6.3.0", "redis": "^4.6.10", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "pdf-parse": "^1.1.1", "mammoth": "^1.6.0", "minio": "^7.1.3", "mime-types": "^2.1.35", "file-type": "^18.7.0", "archiver": "^6.0.1", "winston": "^3.11.0", "express-winston": "^4.2.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "express-validator": "^7.0.1", "uuid": "^9.0.1", "crypto": "^1.0.1", "path": "^0.12.7", "fs-extra": "^11.2.0", "node-cron": "^3.0.3", "axios": "^1.6.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.8.10", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/pg": "^8.10.7", "@types/multer": "^1.4.11", "@types/mime-types": "^2.1.4", "@types/archiver": "^6.0.2", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "@types/fs-extra": "^11.0.4", "@types/node-cron": "^3.0.11", "typescript": "^5.2.2", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.53.0", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "prettier": "^3.0.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}