# 应用配置
NODE_ENV=development
APP_NAME=DocMind User Service
APP_VERSION=1.0.0
APP_PORT=3000
APP_HOST=0.0.0.0

# 数据库配置
DATABASE_URL=postgresql://docmind:docmind123@localhost:5432/docmind
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE=10000

# Redis配置
REDIS_URL=redis://localhost:6379
CACHE_PREFIX=docmind:user:
CACHE_TTL=3600

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=docmind
JWT_AUDIENCE=docmind-users

# 密码加密配置
BCRYPT_ROUNDS=10

# CORS配置
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# 限流配置
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>

# MFA配置
MFA_ISSUER=DocMind
MFA_WINDOW=2

# 文件上传配置
MAX_FILE_SIZE=5MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif
UPLOAD_DESTINATION=uploads/avatars

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=combined
LOG_FILE=logs/user-service.log

# 会话配置
SESSION_SECRET=your-session-secret-key
SESSION_TIMEOUT=86400

# 安全配置
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=true
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

# 监控配置
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true
PROMETHEUS_PORT=9090
