import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../.env') });

interface Config {
  // Application
  app: {
    name: string;
    version: string;
    environment: string;
    port: number;
    host: string;
  };

  // Database
  database: {
    postgres: {
      host: string;
      port: number;
      database: string;
      username: string;
      password: string;
      ssl: boolean;
      pool: {
        min: number;
        max: number;
      };
    };
    mongodb: {
      uri: string;
      database: string;
    };
    redis: {
      host: string;
      port: number;
      password?: string;
      db: number;
    };
  };

  // Authentication
  auth: {
    jwtSecret: string;
    jwtExpiresIn: string;
    refreshTokenExpiresIn: string;
    bcryptRounds: number;
  };

  // External Services
  services: {
    userService: {
      baseUrl: string;
      timeout: number;
    };
    documentService: {
      baseUrl: string;
      timeout: number;
    };
    aiService: {
      baseUrl: string;
      timeout: number;
    };
  };

  // Email
  email: {
    smtp: {
      host: string;
      port: number;
      secure: boolean;
      auth: {
        user: string;
        pass: string;
      };
    };
    from: {
      name: string;
      address: string;
    };
  };

  // File Upload
  upload: {
    maxFileSize: number;
    allowedMimeTypes: string[];
    uploadDir: string;
  };

  // Rate Limiting
  rateLimit: {
    windowMs: number;
    max: number;
  };

  // Logging
  logging: {
    level: string;
    format: string;
    file?: string;
  };

  // WebSocket
  websocket: {
    cors: {
      origin: string[];
      credentials: boolean;
    };
  };

  // Notifications
  notifications: {
    enabled: boolean;
    channels: {
      email: boolean;
      websocket: boolean;
      push: boolean;
    };
  };

  // Workflow
  workflow: {
    defaultApprovers: number;
    autoApprovalThreshold: number;
    escalationTimeoutHours: number;
  };
}

const config: Config = {
  app: {
    name: process.env.APP_NAME || 'DocMind Collaboration Service',
    version: process.env.APP_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT || '3003', 10),
    host: process.env.HOST || '0.0.0.0',
  },

  database: {
    postgres: {
      host: process.env.POSTGRES_HOST || 'localhost',
      port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
      database: process.env.POSTGRES_DB || 'docmind',
      username: process.env.POSTGRES_USER || 'docmind',
      password: process.env.POSTGRES_PASSWORD || 'docmind123',
      ssl: process.env.POSTGRES_SSL === 'true',
      pool: {
        min: parseInt(process.env.POSTGRES_POOL_MIN || '2', 10),
        max: parseInt(process.env.POSTGRES_POOL_MAX || '10', 10),
      },
    },
    mongodb: {
      uri: process.env.MONGODB_URI || '****************************************************',
      database: process.env.MONGODB_DATABASE || 'docmind',
    },
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379', 10),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0', 10),
    },
  },

  auth: {
    jwtSecret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '1h',
    refreshTokenExpiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || '7d',
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
  },

  services: {
    userService: {
      baseUrl: process.env.USER_SERVICE_URL || 'http://localhost:3001',
      timeout: parseInt(process.env.USER_SERVICE_TIMEOUT || '5000', 10),
    },
    documentService: {
      baseUrl: process.env.DOCUMENT_SERVICE_URL || 'http://localhost:3002',
      timeout: parseInt(process.env.DOCUMENT_SERVICE_TIMEOUT || '10000', 10),
    },
    aiService: {
      baseUrl: process.env.AI_SERVICE_URL || 'http://localhost:8001',
      timeout: parseInt(process.env.AI_SERVICE_TIMEOUT || '30000', 10),
    },
  },

  email: {
    smtp: {
      host: process.env.SMTP_HOST || 'localhost',
      port: parseInt(process.env.SMTP_PORT || '587', 10),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER || '',
        pass: process.env.SMTP_PASS || '',
      },
    },
    from: {
      name: process.env.EMAIL_FROM_NAME || 'DocMind',
      address: process.env.EMAIL_FROM_ADDRESS || '<EMAIL>',
    },
  },

  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760', 10), // 10MB
    allowedMimeTypes: (process.env.ALLOWED_MIME_TYPES || 'image/jpeg,image/png,image/gif,application/pdf').split(','),
    uploadDir: process.env.UPLOAD_DIR || 'uploads',
  },

  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX || '100', 10),
  },

  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'combined',
    file: process.env.LOG_FILE,
  },

  websocket: {
    cors: {
      origin: (process.env.WEBSOCKET_CORS_ORIGIN || 'http://localhost:3000').split(','),
      credentials: process.env.WEBSOCKET_CORS_CREDENTIALS === 'true',
    },
  },

  notifications: {
    enabled: process.env.NOTIFICATIONS_ENABLED !== 'false',
    channels: {
      email: process.env.NOTIFICATIONS_EMAIL !== 'false',
      websocket: process.env.NOTIFICATIONS_WEBSOCKET !== 'false',
      push: process.env.NOTIFICATIONS_PUSH === 'true',
    },
  },

  workflow: {
    defaultApprovers: parseInt(process.env.WORKFLOW_DEFAULT_APPROVERS || '2', 10),
    autoApprovalThreshold: parseInt(process.env.WORKFLOW_AUTO_APPROVAL_THRESHOLD || '1000', 10),
    escalationTimeoutHours: parseInt(process.env.WORKFLOW_ESCALATION_TIMEOUT_HOURS || '24', 10),
  },
};

export default config;
