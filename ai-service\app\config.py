import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field

class Settings(BaseSettings):
    # Application settings
    app_name: str = Field(default="DocMind AI Service", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=True, env="DEBUG")
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8001, env="PORT")
    
    # API settings
    api_prefix: str = Field(default="/api/v1", env="API_PREFIX")
    docs_url: str = Field(default="/docs", env="DOCS_URL")
    redoc_url: str = Field(default="/redoc", env="REDOC_URL")
    
    # Security settings
    secret_key: str = Field(default="your-secret-key-change-in-production", env="SECRET_KEY")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    allowed_hosts: List[str] = Field(default=["*"], env="ALLOWED_HOSTS")
    cors_origins: List[str] = Field(default=["*"], env="CORS_ORIGINS")
    
    # Database settings
    mongodb_url: str = Field(default="****************************************************", env="MONGODB_URL")
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    postgres_url: str = Field(default="postgresql://docmind:docmind123@localhost:5432/docmind", env="POSTGRES_URL")
    
    # File storage settings
    upload_dir: str = Field(default="uploads", env="UPLOAD_DIR")
    temp_dir: str = Field(default="temp", env="TEMP_DIR")
    max_file_size: int = Field(default=100 * 1024 * 1024, env="MAX_FILE_SIZE")  # 100MB
    allowed_file_types: List[str] = Field(
        default=["pdf", "doc", "docx", "txt", "jpg", "jpeg", "png"],
        env="ALLOWED_FILE_TYPES"
    )
    
    # OCR settings
    ocr_enabled: bool = Field(default=True, env="OCR_ENABLED")
    tesseract_path: Optional[str] = Field(default=None, env="TESSERACT_PATH")
    ocr_languages: List[str] = Field(default=["eng", "chi_sim"], env="OCR_LANGUAGES")
    ocr_confidence_threshold: float = Field(default=0.6, env="OCR_CONFIDENCE_THRESHOLD")
    
    # NLP settings
    nlp_enabled: bool = Field(default=True, env="NLP_ENABLED")
    spacy_model: str = Field(default="zh_core_web_sm", env="SPACY_MODEL")
    sentence_transformer_model: str = Field(
        default="sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
        env="SENTENCE_TRANSFORMER_MODEL"
    )
    
    # AI model settings
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-3.5-turbo", env="OPENAI_MODEL")
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    
    # Risk analysis settings
    risk_analysis_enabled: bool = Field(default=True, env="RISK_ANALYSIS_ENABLED")
    risk_threshold_high: float = Field(default=0.8, env="RISK_THRESHOLD_HIGH")
    risk_threshold_medium: float = Field(default=0.5, env="RISK_THRESHOLD_MEDIUM")
    
    # Contract analysis settings
    contract_analysis_enabled: bool = Field(default=True, env="CONTRACT_ANALYSIS_ENABLED")
    contract_templates_dir: str = Field(default="templates/contracts", env="CONTRACT_TEMPLATES_DIR")
    
    # Processing settings
    max_concurrent_tasks: int = Field(default=5, env="MAX_CONCURRENT_TASKS")
    task_timeout: int = Field(default=300, env="TASK_TIMEOUT")  # 5 minutes
    batch_size: int = Field(default=10, env="BATCH_SIZE")
    
    # Cache settings
    cache_enabled: bool = Field(default=True, env="CACHE_ENABLED")
    cache_ttl: int = Field(default=3600, env="CACHE_TTL")  # 1 hour
    cache_prefix: str = Field(default="docmind:ai:", env="CACHE_PREFIX")
    
    # Logging settings
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="json", env="LOG_FORMAT")
    log_file: Optional[str] = Field(default=None, env="LOG_FILE")
    
    # Monitoring settings
    metrics_enabled: bool = Field(default=True, env="METRICS_ENABLED")
    health_check_interval: int = Field(default=30, env="HEALTH_CHECK_INTERVAL")
    
    # External services
    document_service_url: str = Field(default="http://localhost:3002", env="DOCUMENT_SERVICE_URL")
    user_service_url: str = Field(default="http://localhost:3001", env="USER_SERVICE_URL")
    
    # Model paths
    models_dir: str = Field(default="models", env="MODELS_DIR")
    custom_models_enabled: bool = Field(default=False, env="CUSTOM_MODELS_ENABLED")
    
    # Language settings
    default_language: str = Field(default="zh", env="DEFAULT_LANGUAGE")
    supported_languages: List[str] = Field(default=["zh", "en"], env="SUPPORTED_LANGUAGES")
    
    # Feature flags
    experimental_features: bool = Field(default=False, env="EXPERIMENTAL_FEATURES")
    beta_features: bool = Field(default=False, env="BETA_FEATURES")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

# Global settings instance
settings = Settings()

# Model configurations
MODEL_CONFIGS = {
    "ocr": {
        "tesseract": {
            "config": "--oem 3 --psm 6",
            "languages": settings.ocr_languages,
            "confidence_threshold": settings.ocr_confidence_threshold
        }
    },
    "nlp": {
        "spacy": {
            "model": settings.spacy_model,
            "components": ["tok2vec", "tagger", "parser", "ner"]
        },
        "sentence_transformer": {
            "model": settings.sentence_transformer_model,
            "max_seq_length": 512
        }
    },
    "risk_analysis": {
        "thresholds": {
            "high": settings.risk_threshold_high,
            "medium": settings.risk_threshold_medium,
            "low": 0.0
        },
        "categories": [
            "payment_terms",
            "liability",
            "termination",
            "intellectual_property",
            "confidentiality",
            "compliance",
            "force_majeure"
        ]
    }
}

# Contract templates configuration
CONTRACT_TEMPLATES = {
    "service_agreement": {
        "name": "服务协议",
        "key_clauses": [
            "service_scope",
            "payment_terms",
            "service_level",
            "liability",
            "termination"
        ]
    },
    "purchase_agreement": {
        "name": "采购协议",
        "key_clauses": [
            "product_specification",
            "delivery_terms",
            "payment_terms",
            "quality_standards",
            "warranty"
        ]
    },
    "employment_contract": {
        "name": "劳动合同",
        "key_clauses": [
            "job_description",
            "compensation",
            "working_hours",
            "confidentiality",
            "termination"
        ]
    },
    "nda": {
        "name": "保密协议",
        "key_clauses": [
            "confidential_information",
            "obligations",
            "duration",
            "exceptions",
            "remedies"
        ]
    }
}

# Risk patterns for different contract types
RISK_PATTERNS = {
    "high_risk": [
        "unlimited liability",
        "无限责任",
        "personal guarantee",
        "个人担保",
        "automatic renewal",
        "自动续约",
        "exclusive dealing",
        "独家交易"
    ],
    "medium_risk": [
        "penalty clause",
        "违约金条款",
        "termination for convenience",
        "便利终止",
        "intellectual property assignment",
        "知识产权转让"
    ],
    "compliance_risk": [
        "regulatory compliance",
        "合规要求",
        "data protection",
        "数据保护",
        "anti-corruption",
        "反腐败"
    ]
}

def get_model_config(model_type: str, model_name: str = None):
    """Get configuration for a specific model"""
    if model_type in MODEL_CONFIGS:
        if model_name and model_name in MODEL_CONFIGS[model_type]:
            return MODEL_CONFIGS[model_type][model_name]
        return MODEL_CONFIGS[model_type]
    return None

def get_contract_template(template_type: str):
    """Get contract template configuration"""
    return CONTRACT_TEMPLATES.get(template_type)

def get_risk_patterns(risk_level: str = None):
    """Get risk patterns for analysis"""
    if risk_level and risk_level in RISK_PATTERNS:
        return RISK_PATTERNS[risk_level]
    return RISK_PATTERNS
