import Jo<PERSON> from 'joi';
import { config } from '@/config';

// 基础验证规则
const emailSchema = Joi.string()
  .email()
  .max(255)
  .required()
  .messages({
    'string.email': 'Please provide a valid email address',
    'string.max': 'Email must not exceed 255 characters',
    'any.required': 'Email is required'
  });

const passwordSchema = Joi.string()
  .min(config.security.passwordMinLength)
  .max(128)
  .required()
  .messages({
    'string.min': `Password must be at least ${config.security.passwordMinLength} characters long`,
    'string.max': 'Password must not exceed 128 characters',
    'any.required': 'Password is required'
  });

const nameSchema = Joi.string()
  .min(1)
  .max(100)
  .required()
  .messages({
    'string.min': 'Name is required',
    'string.max': 'Name must not exceed 100 characters',
    'any.required': 'Name is required'
  });

const phoneSchema = Joi.string()
  .pattern(/^\+?[1-9]\d{1,14}$/)
  .optional()
  .messages({
    'string.pattern.base': 'Please provide a valid phone number'
  });

const uuidSchema = Joi.string()
  .uuid()
  .required()
  .messages({
    'string.uuid': 'Invalid ID format',
    'any.required': 'ID is required'
  });

// 用户注册验证
export const registerSchema = Joi.object({
  email: emailSchema,
  password: passwordSchema,
  name: nameSchema,
  phone: phoneSchema
});

// 用户登录验证
export const loginSchema = Joi.object({
  email: emailSchema,
  password: Joi.string().required().messages({
    'any.required': 'Password is required'
  }),
  remember_me: Joi.boolean().optional()
});

// 用户更新验证
export const updateUserSchema = Joi.object({
  name: Joi.string().min(1).max(100).optional(),
  phone: phoneSchema,
  avatar_url: Joi.string().uri().max(500).optional().allow('')
});

// 密码更改验证
export const changePasswordSchema = Joi.object({
  current_password: Joi.string().required().messages({
    'any.required': 'Current password is required'
  }),
  new_password: passwordSchema
});

// 密码重置请求验证
export const passwordResetRequestSchema = Joi.object({
  email: emailSchema
});

// 密码重置确认验证
export const passwordResetConfirmSchema = Joi.object({
  token: Joi.string().required().messages({
    'any.required': 'Reset token is required'
  }),
  new_password: passwordSchema
});

// 邮箱验证令牌验证
export const emailVerificationSchema = Joi.object({
  token: Joi.string().required().messages({
    'any.required': 'Verification token is required'
  })
});

// 刷新令牌验证
export const refreshTokenSchema = Joi.object({
  refresh_token: Joi.string().required().messages({
    'any.required': 'Refresh token is required'
  })
});

// 组织创建验证
export const createOrganizationSchema = Joi.object({
  name: Joi.string().min(1).max(200).required().messages({
    'string.min': 'Organization name is required',
    'string.max': 'Organization name must not exceed 200 characters',
    'any.required': 'Organization name is required'
  }),
  domain: Joi.string().domain().max(100).optional(),
  logo_url: Joi.string().uri().max(500).optional().allow('')
});

// 组织更新验证
export const updateOrganizationSchema = Joi.object({
  name: Joi.string().min(1).max(200).optional(),
  domain: Joi.string().domain().max(100).optional().allow(''),
  logo_url: Joi.string().uri().max(500).optional().allow(''),
  settings: Joi.object().optional()
});

// 用户邀请验证
export const inviteUserSchema = Joi.object({
  email: emailSchema,
  role: Joi.string().valid('owner', 'admin', 'manager', 'member', 'viewer').required(),
  permissions: Joi.object().optional()
});

// MFA验证
export const mfaVerifySchema = Joi.object({
  token: Joi.string().required(),
  code: Joi.string().length(6).pattern(/^\d+$/).required().messages({
    'string.length': 'MFA code must be 6 digits',
    'string.pattern.base': 'MFA code must contain only numbers'
  })
});

// 分页参数验证
export const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  sort: Joi.string().valid('created_at', 'updated_at', 'name', 'email').default('created_at'),
  order: Joi.string().valid('asc', 'desc').default('desc'),
  search: Joi.string().max(255).optional(),
  status: Joi.string().valid('active', 'inactive', 'suspended').optional()
});

// ID参数验证
export const idParamSchema = Joi.object({
  id: uuidSchema
});

// 验证中间件工厂函数
export function validateBody(schema: Joi.ObjectSchema) {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors: Record<string, string[]> = {};
      error.details.forEach((detail) => {
        const field = detail.path.join('.');
        if (!errors[field]) {
          errors[field] = [];
        }
        errors[field].push(detail.message);
      });

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors
      });
    }

    req.body = value;
    next();
  };
}

export function validateQuery(schema: Joi.ObjectSchema) {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors: Record<string, string[]> = {};
      error.details.forEach((detail) => {
        const field = detail.path.join('.');
        if (!errors[field]) {
          errors[field] = [];
        }
        errors[field].push(detail.message);
      });

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors
      });
    }

    req.query = value;
    next();
  };
}

export function validateParams(schema: Joi.ObjectSchema) {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.params, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors: Record<string, string[]> = {};
      error.details.forEach((detail) => {
        const field = detail.path.join('.');
        if (!errors[field]) {
          errors[field] = [];
        }
        errors[field].push(detail.message);
      });

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors
      });
    }

    req.params = value;
    next();
  };
}

// 自定义验证函数
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

export function sanitizeString(str: string): string {
  return str.trim().replace(/\s+/g, ' ');
}
