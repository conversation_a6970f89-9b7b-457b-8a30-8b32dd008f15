import { UserModel } from '@/models/User';
import { PasswordService } from '@/utils/password';
import { JWTService } from '@/utils/jwt';
import { cacheService } from '@/config/redis';
import { createError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';
import {
  User,
  PublicUser,
  CreateUserRequest,
  UpdateUserRequest,
  LoginRequest,
  LoginResponse,
  ChangePasswordRequest,
  PaginationParams
} from '@/types/user';

export class UserService {
  /**
   * 用户注册
   */
  static async register(userData: CreateUserRequest): Promise<PublicUser> {
    try {
      // 检查邮箱是否已存在
      const existingUser = await UserModel.findByEmail(userData.email);
      if (existingUser) {
        throw createError.conflict('Email already registered');
      }

      // 验证密码强度
      const passwordValidation = PasswordService.validatePasswordStrength(userData.password);
      if (!passwordValidation.isValid) {
        throw createError.validation(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
      }

      // 检查是否是常见密码
      if (PasswordService.isCommonPassword(userData.password)) {
        throw createError.validation('Password is too common, please choose a stronger password');
      }

      // 哈希密码
      const passwordHash = await PasswordService.hashPassword(userData.password);

      // 创建用户
      const user = await UserModel.create({
        ...userData,
        password_hash: passwordHash
      });

      logger.info('User registered successfully:', { userId: user.id, email: user.email });

      return this.toPublicUser(user);
    } catch (error) {
      logger.error('User registration failed:', error);
      throw error;
    }
  }

  /**
   * 用户登录
   */
  static async login(loginData: LoginRequest): Promise<LoginResponse> {
    try {
      // 查找用户
      const user = await UserModel.findByEmail(loginData.email);
      if (!user) {
        throw createError.authentication('Invalid email or password');
      }

      // 检查用户状态
      if (user.status !== 'active') {
        throw createError.authentication('Account is not active');
      }

      // 验证密码
      const isPasswordValid = await PasswordService.verifyPassword(loginData.password, user.password_hash);
      if (!isPasswordValid) {
        throw createError.authentication('Invalid email or password');
      }

      // 更新最后登录时间
      await UserModel.updateLastLogin(user.id);

      // 生成JWT令牌
      const accessToken = JWTService.generateAccessToken(user);
      const refreshToken = JWTService.generateRefreshToken(user.id);

      // 缓存用户信息
      await cacheService.set(`user:${user.id}`, user, 3600);

      logger.info('User logged in successfully:', { userId: user.id, email: user.email });

      return {
        user: this.toPublicUser(user),
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: 24 * 60 * 60 // 24小时（秒）
      };
    } catch (error) {
      logger.error('User login failed:', error);
      throw error;
    }
  }

  /**
   * 刷新访问令牌
   */
  static async refreshToken(refreshToken: string): Promise<{ access_token: string; expires_in: number }> {
    try {
      // 验证刷新令牌
      const payload = JWTService.verifyRefreshToken(refreshToken);
      if (!payload) {
        throw createError.authentication('Invalid refresh token');
      }

      // 查找用户
      const user = await UserModel.findById(payload.sub);
      if (!user) {
        throw createError.authentication('User not found');
      }

      if (user.status !== 'active') {
        throw createError.authentication('Account is not active');
      }

      // 生成新的访问令牌
      const accessToken = JWTService.generateAccessToken(user);

      return {
        access_token: accessToken,
        expires_in: 24 * 60 * 60
      };
    } catch (error) {
      logger.error('Token refresh failed:', error);
      throw error;
    }
  }

  /**
   * 获取用户信息
   */
  static async getUserById(id: string): Promise<PublicUser | null> {
    try {
      // 先从缓存获取
      let user = await cacheService.get<User>(`user:${id}`);
      
      if (!user) {
        // 缓存中没有，从数据库获取
        user = await UserModel.findById(id);
        if (user) {
          await cacheService.set(`user:${id}`, user, 3600);
        }
      }

      return user ? this.toPublicUser(user) : null;
    } catch (error) {
      logger.error('Failed to get user by id:', error);
      throw error;
    }
  }

  /**
   * 更新用户信息
   */
  static async updateUser(id: string, updateData: UpdateUserRequest): Promise<PublicUser | null> {
    try {
      const updatedUser = await UserModel.update(id, updateData);
      if (!updatedUser) {
        throw createError.notFound('User not found');
      }

      // 更新缓存
      await cacheService.set(`user:${id}`, updatedUser, 3600);

      logger.info('User updated successfully:', { userId: id });

      return this.toPublicUser(updatedUser);
    } catch (error) {
      logger.error('Failed to update user:', error);
      throw error;
    }
  }

  /**
   * 更改密码
   */
  static async changePassword(id: string, passwordData: ChangePasswordRequest): Promise<void> {
    try {
      // 获取用户
      const user = await UserModel.findById(id);
      if (!user) {
        throw createError.notFound('User not found');
      }

      // 验证当前密码
      const isCurrentPasswordValid = await PasswordService.verifyPassword(
        passwordData.current_password,
        user.password_hash
      );
      if (!isCurrentPasswordValid) {
        throw createError.authentication('Current password is incorrect');
      }

      // 验证新密码强度
      const passwordValidation = PasswordService.validatePasswordStrength(passwordData.new_password);
      if (!passwordValidation.isValid) {
        throw createError.validation(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
      }

      // 检查新密码是否与当前密码相同
      const isSamePassword = await PasswordService.verifyPassword(
        passwordData.new_password,
        user.password_hash
      );
      if (isSamePassword) {
        throw createError.validation('New password must be different from current password');
      }

      // 哈希新密码
      const newPasswordHash = await PasswordService.hashPassword(passwordData.new_password);

      // 更新密码
      await UserModel.updatePassword(id, newPasswordHash);

      // 清除用户缓存
      await cacheService.del(`user:${id}`);

      logger.info('Password changed successfully:', { userId: id });
    } catch (error) {
      logger.error('Failed to change password:', error);
      throw error;
    }
  }

  /**
   * 删除用户
   */
  static async deleteUser(id: string): Promise<void> {
    try {
      const deleted = await UserModel.delete(id);
      if (!deleted) {
        throw createError.notFound('User not found');
      }

      // 清除缓存
      await cacheService.del(`user:${id}`);

      logger.info('User deleted successfully:', { userId: id });
    } catch (error) {
      logger.error('Failed to delete user:', error);
      throw error;
    }
  }

  /**
   * 获取用户列表
   */
  static async getUsers(params: PaginationParams & { 
    search?: string;
    status?: string;
  }): Promise<{
    users: PublicUser[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
      has_next: boolean;
      has_prev: boolean;
    };
  }> {
    try {
      const { users, total } = await UserModel.findMany(params);
      const { page = 1, limit = 20 } = params;
      const pages = Math.ceil(total / limit);

      return {
        users: users.map(user => this.toPublicUser(user)),
        pagination: {
          page,
          limit,
          total,
          pages,
          has_next: page < pages,
          has_prev: page > 1
        }
      };
    } catch (error) {
      logger.error('Failed to get users:', error);
      throw error;
    }
  }

  /**
   * 验证邮箱
   */
  static async verifyEmail(userId: string): Promise<void> {
    try {
      const verified = await UserModel.verifyEmail(userId);
      if (!verified) {
        throw createError.notFound('User not found');
      }

      // 清除缓存
      await cacheService.del(`user:${userId}`);

      logger.info('Email verified successfully:', { userId });
    } catch (error) {
      logger.error('Failed to verify email:', error);
      throw error;
    }
  }

  /**
   * 获取用户统计信息
   */
  static async getUserStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    suspended: number;
    verified: number;
    unverified: number;
  }> {
    try {
      return await UserModel.getStats();
    } catch (error) {
      logger.error('Failed to get user stats:', error);
      throw error;
    }
  }

  /**
   * 将User对象转换为PublicUser对象（移除敏感信息）
   */
  private static toPublicUser(user: User): PublicUser {
    const { password_hash, ...publicUser } = user;
    return publicUser;
  }
}
