import { Request, Response } from 'express';
import { getPool } from '@/config/database';
import { getRedisClient } from '@/config/redis';
import { asyncHandler } from '@/middleware/errorHandler';
import { config } from '@/config';

export class HealthController {
  /**
   * 基础健康检查
   */
  static healthCheck = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const response = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: config.app.name,
      version: config.app.version,
      environment: config.app.env,
      uptime: process.uptime()
    };

    res.json(response);
  });

  /**
   * 详细健康检查
   */
  static detailedHealthCheck = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const checks = {
      database: await this.checkDatabase(),
      redis: await this.checkRedis(),
      memory: this.checkMemory(),
      disk: this.checkDisk()
    };

    const isHealthy = Object.values(checks).every(check => check.status === 'healthy');
    const status = isHealthy ? 'healthy' : 'unhealthy';

    const response = {
      status,
      timestamp: new Date().toISOString(),
      service: config.app.name,
      version: config.app.version,
      environment: config.app.env,
      uptime: process.uptime(),
      checks
    };

    res.status(isHealthy ? 200 : 503).json(response);
  });

  /**
   * 就绪检查
   */
  static readinessCheck = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    try {
      // 检查数据库连接
      await this.checkDatabase();
      
      // 检查Redis连接
      await this.checkRedis();

      res.json({
        status: 'ready',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(503).json({
        status: 'not ready',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  /**
   * 存活检查
   */
  static livenessCheck = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    res.json({
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    });
  });

  /**
   * 检查数据库连接
   */
  private static async checkDatabase(): Promise<{ status: string; responseTime?: number; error?: string }> {
    try {
      const start = Date.now();
      const pool = getPool();
      const client = await pool.connect();
      await client.query('SELECT 1');
      client.release();
      const responseTime = Date.now() - start;

      return {
        status: 'healthy',
        responseTime
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown database error'
      };
    }
  }

  /**
   * 检查Redis连接
   */
  private static async checkRedis(): Promise<{ status: string; responseTime?: number; error?: string }> {
    try {
      const start = Date.now();
      const redis = getRedisClient();
      await redis.ping();
      const responseTime = Date.now() - start;

      return {
        status: 'healthy',
        responseTime
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown Redis error'
      };
    }
  }

  /**
   * 检查内存使用情况
   */
  private static checkMemory(): { status: string; usage: NodeJS.MemoryUsage; percentage: number } {
    const usage = process.memoryUsage();
    const totalMemory = usage.heapTotal;
    const usedMemory = usage.heapUsed;
    const percentage = (usedMemory / totalMemory) * 100;

    return {
      status: percentage > 90 ? 'warning' : 'healthy',
      usage,
      percentage: Math.round(percentage * 100) / 100
    };
  }

  /**
   * 检查磁盘使用情况
   */
  private static checkDisk(): { status: string; message: string } {
    // 简单的磁盘检查，实际应用中可能需要更复杂的逻辑
    return {
      status: 'healthy',
      message: 'Disk check not implemented'
    };
  }

  /**
   * 获取系统信息
   */
  static getSystemInfo = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const systemInfo = {
      service: {
        name: config.app.name,
        version: config.app.version,
        environment: config.app.env,
        uptime: process.uptime(),
        pid: process.pid
      },
      node: {
        version: process.version,
        platform: process.platform,
        arch: process.arch
      },
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      timestamp: new Date().toISOString()
    };

    res.json(systemInfo);
  });
}
