import ApiService from './api';
import {
  Document,
  DocumentUploadForm,
  DocumentUpdateForm,
  DocumentSearchParams,
  DocumentVersion,
  DocumentAnalysisResult,
  RiskAnalysisResult,
  PaginatedResponse,
  ApiResponse,
} from '@/types';

export class DocumentService {
  // 获取文档列表
  static async getDocuments(params?: DocumentSearchParams): Promise<PaginatedResponse<Document>> {
    const response = await ApiService.get<PaginatedResponse<Document>>('/api/documents', {
      params,
    });
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '获取文档列表失败');
  }

  // 搜索文档
  static async searchDocuments(params: DocumentSearchParams): Promise<PaginatedResponse<Document>> {
    const response = await ApiService.get<PaginatedResponse<Document>>('/api/documents/search', {
      params,
    });
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '搜索文档失败');
  }

  // 根据ID获取文档
  static async getDocumentById(id: string): Promise<Document> {
    const response = await ApiService.get<Document>(`/api/documents/${id}`);
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '获取文档详情失败');
  }

  // 上传文档
  static async uploadDocument(
    data: DocumentUploadForm,
    onProgress?: (progress: number) => void
  ): Promise<Document> {
    const { file, ...formData } = data;
    
    const response = await ApiService.upload<Document>(
      '/api/documents/upload',
      file,
      formData,
      onProgress
    );
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '文档上传失败');
  }

  // 更新文档信息
  static async updateDocument(id: string, data: DocumentUpdateForm): Promise<Document> {
    const response = await ApiService.put<Document>(`/api/documents/${id}`, data);
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '更新文档失败');
  }

  // 删除文档
  static async deleteDocument(id: string): Promise<void> {
    const response = await ApiService.delete(`/api/documents/${id}`);
    
    if (!response.success) {
      throw new Error(response.message || '删除文档失败');
    }
  }

  // 批量删除文档
  static async bulkDeleteDocuments(documentIds: string[]): Promise<void> {
    const response = await ApiService.post('/api/documents/bulk/delete', {
      document_ids: documentIds,
    });
    
    if (!response.success) {
      throw new Error(response.message || '批量删除文档失败');
    }
  }

  // 下载文档
  static async downloadDocument(id: string, filename?: string): Promise<void> {
    await ApiService.download(`/api/documents/${id}/download`, filename);
  }

  // 获取文档版本列表
  static async getDocumentVersions(documentId: string): Promise<DocumentVersion[]> {
    const response = await ApiService.get<DocumentVersion[]>(`/api/documents/${documentId}/versions`);
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '获取文档版本失败');
  }

  // 创建文档版本
  static async createDocumentVersion(
    documentId: string,
    file: File,
    changesSummary?: string,
    onProgress?: (progress: number) => void
  ): Promise<Document> {
    const response = await ApiService.upload<Document>(
      `/api/documents/${documentId}/versions`,
      file,
      { changes_summary: changesSummary },
      onProgress
    );
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '创建文档版本失败');
  }

  // 获取文档分析结果
  static async getDocumentAnalysis(documentId: string): Promise<DocumentAnalysisResult> {
    const response = await ApiService.get<DocumentAnalysisResult>(`/api/documents/${documentId}/analysis`);
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '获取文档分析结果失败');
  }

  // 获取风险分析结果
  static async getRiskAnalysis(documentId: string): Promise<RiskAnalysisResult> {
    const response = await ApiService.get<RiskAnalysisResult>(`/api/documents/${documentId}/risk-analysis`);
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '获取风险分析结果失败');
  }

  // 重新分析文档
  static async reanalyzeDocument(documentId: string): Promise<void> {
    const response = await ApiService.post(`/api/documents/${documentId}/reanalyze`);
    
    if (!response.success) {
      throw new Error(response.message || '重新分析文档失败');
    }
  }

  // 获取文档预览
  static async getDocumentPreview(documentId: string): Promise<string> {
    const response = await ApiService.get<{ preview_url: string }>(`/api/documents/${documentId}/preview`);
    
    if (response.success && response.data) {
      return response.data.preview_url;
    }
    
    throw new Error(response.message || '获取文档预览失败');
  }

  // 获取文档缩略图
  static async getDocumentThumbnail(documentId: string): Promise<string> {
    const response = await ApiService.get<{ thumbnail_url: string }>(`/api/documents/${documentId}/thumbnail`);
    
    if (response.success && response.data) {
      return response.data.thumbnail_url;
    }
    
    throw new Error(response.message || '获取文档缩略图失败');
  }

  // 获取文档统计信息
  static async getDocumentStats(organizationId?: string): Promise<any> {
    const response = await ApiService.get('/api/documents/stats', {
      params: { organization_id: organizationId },
    });
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '获取文档统计失败');
  }

  // 导出文档数据
  static async exportDocuments(params?: DocumentSearchParams): Promise<void> {
    await ApiService.download('/api/documents/export', 'documents.xlsx', {
      params,
    });
  }

  // 获取支持的文件类型
  static async getSupportedFileTypes(): Promise<string[]> {
    const response = await ApiService.get<{ file_types: string[] }>('/api/documents/supported-types');
    
    if (response.success && response.data) {
      return response.data.file_types;
    }
    
    throw new Error(response.message || '获取支持的文件类型失败');
  }

  // 检查文件大小限制
  static async getUploadLimits(): Promise<{
    max_file_size: number;
    max_files_per_upload: number;
    allowed_types: string[];
  }> {
    const response = await ApiService.get('/api/documents/upload-limits');
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '获取上传限制失败');
  }

  // 验证文件
  static async validateFile(file: File): Promise<{
    valid: boolean;
    errors: string[];
  }> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await ApiService.post('/api/documents/validate', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    if (response.success && response.data) {
      return response.data;
    }
    
    throw new Error(response.message || '文件验证失败');
  }

  // 获取文档标签建议
  static async getTagSuggestions(query: string): Promise<string[]> {
    const response = await ApiService.get<{ tags: string[] }>('/api/documents/tags/suggestions', {
      params: { q: query },
    });
    
    if (response.success && response.data) {
      return response.data.tags;
    }
    
    return [];
  }

  // 获取热门标签
  static async getPopularTags(limit: number = 20): Promise<Array<{ tag: string; count: number }>> {
    const response = await ApiService.get('/api/documents/tags/popular', {
      params: { limit },
    });
    
    if (response.success && response.data) {
      return response.data;
    }
    
    return [];
  }
}

export default DocumentService;
