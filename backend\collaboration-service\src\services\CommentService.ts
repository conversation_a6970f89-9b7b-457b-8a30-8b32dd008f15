import { v4 as uuidv4 } from 'uuid';
import { CommentModel } from '../models/Comment';
import { NotificationService } from './NotificationService';
import { WebSocketService } from './WebSocketService';
import { ActivityLogService } from './ActivityLogService';
import { logger } from '../utils/logger';
import {
  Comment,
  CreateCommentRequest,
  UpdateCommentRequest,
  CommentSearchParams,
  CommentStatus,
  PaginatedResponse,
  NotificationType,
  ActivityAction,
  ResourceType,
  WebSocketMessageType,
} from '../types';

export class CommentService {
  /**
   * 创建评论
   */
  static async createComment(
    data: CreateCommentRequest,
    authorId: string
  ): Promise<Comment> {
    try {
      const commentData = {
        id: uuidv4(),
        ...data,
        author_id: authorId,
        mentions: data.mentions || [],
        attachments: [],
        reactions: [],
        status: CommentStatus.ACTIVE,
        created_at: new Date(),
        updated_at: new Date(),
      };

      const comment = await CommentModel.create(commentData);

      // 记录活动日志
      await ActivityLogService.log({
        user_id: authorId,
        action: ActivityAction.COMMENT,
        resource_type: ResourceType.COMMENT,
        resource_id: comment.id,
        details: {
          document_id: data.document_id,
          parent_id: data.parent_id,
          content_length: data.content.length,
        },
      });

      // 发送通知给被提及的用户
      if (data.mentions && data.mentions.length > 0) {
        await this.sendMentionNotifications(comment, data.mentions);
      }

      // 如果是回复，通知父评论作者
      if (data.parent_id) {
        await this.sendReplyNotification(comment);
      }

      // 实时推送
      await WebSocketService.broadcastToDocument(data.document_id, {
        type: WebSocketMessageType.COMMENT_CREATED,
        data: comment,
        timestamp: new Date(),
      });

      logger.info('Comment created successfully', {
        commentId: comment.id,
        documentId: data.document_id,
        authorId,
      });

      return comment;
    } catch (error) {
      logger.error('Failed to create comment', { error, data, authorId });
      throw error;
    }
  }

  /**
   * 获取评论列表
   */
  static async getComments(params: CommentSearchParams): Promise<PaginatedResponse<Comment>> {
    try {
      return await CommentModel.search(params);
    } catch (error) {
      logger.error('Failed to get comments', { error, params });
      throw error;
    }
  }

  /**
   * 根据ID获取评论
   */
  static async getCommentById(id: string): Promise<Comment | null> {
    try {
      return await CommentModel.findById(id);
    } catch (error) {
      logger.error('Failed to get comment by id', { error, id });
      throw error;
    }
  }

  /**
   * 更新评论
   */
  static async updateComment(
    id: string,
    data: UpdateCommentRequest,
    userId: string
  ): Promise<Comment | null> {
    try {
      const comment = await CommentModel.findById(id);
      if (!comment) {
        return null;
      }

      // 检查权限（只有作者可以编辑）
      if (comment.author_id !== userId) {
        throw new Error('Permission denied');
      }

      const updatedComment = await CommentModel.update(id, {
        ...data,
        updated_at: new Date(),
      });

      if (updatedComment) {
        // 记录活动日志
        await ActivityLogService.log({
          user_id: userId,
          action: ActivityAction.UPDATE,
          resource_type: ResourceType.COMMENT,
          resource_id: id,
          details: { changes: data },
        });

        // 实时推送
        await WebSocketService.broadcastToDocument(updatedComment.document_id, {
          type: WebSocketMessageType.COMMENT_UPDATED,
          data: updatedComment,
          timestamp: new Date(),
        });

        logger.info('Comment updated successfully', { commentId: id, userId });
      }

      return updatedComment;
    } catch (error) {
      logger.error('Failed to update comment', { error, id, data, userId });
      throw error;
    }
  }

  /**
   * 删除评论
   */
  static async deleteComment(id: string, userId: string): Promise<boolean> {
    try {
      const comment = await CommentModel.findById(id);
      if (!comment) {
        return false;
      }

      // 检查权限（只有作者可以删除）
      if (comment.author_id !== userId) {
        throw new Error('Permission denied');
      }

      // 软删除：更新状态为已删除
      const success = await CommentModel.update(id, {
        status: CommentStatus.DELETED,
        updated_at: new Date(),
      });

      if (success) {
        // 记录活动日志
        await ActivityLogService.log({
          user_id: userId,
          action: ActivityAction.DELETE,
          resource_type: ResourceType.COMMENT,
          resource_id: id,
          details: { document_id: comment.document_id },
        });

        // 实时推送
        await WebSocketService.broadcastToDocument(comment.document_id, {
          type: WebSocketMessageType.COMMENT_DELETED,
          data: { id, document_id: comment.document_id },
          timestamp: new Date(),
        });

        logger.info('Comment deleted successfully', { commentId: id, userId });
      }

      return !!success;
    } catch (error) {
      logger.error('Failed to delete comment', { error, id, userId });
      throw error;
    }
  }

  /**
   * 解决评论
   */
  static async resolveComment(id: string, userId: string): Promise<Comment | null> {
    try {
      const updatedComment = await CommentModel.update(id, {
        status: CommentStatus.RESOLVED,
        resolved_at: new Date(),
        resolved_by: userId,
        updated_at: new Date(),
      });

      if (updatedComment) {
        // 记录活动日志
        await ActivityLogService.log({
          user_id: userId,
          action: ActivityAction.UPDATE,
          resource_type: ResourceType.COMMENT,
          resource_id: id,
          details: { action: 'resolve' },
        });

        // 实时推送
        await WebSocketService.broadcastToDocument(updatedComment.document_id, {
          type: WebSocketMessageType.COMMENT_UPDATED,
          data: updatedComment,
          timestamp: new Date(),
        });

        logger.info('Comment resolved successfully', { commentId: id, userId });
      }

      return updatedComment;
    } catch (error) {
      logger.error('Failed to resolve comment', { error, id, userId });
      throw error;
    }
  }

  /**
   * 添加评论反应
   */
  static async addReaction(
    commentId: string,
    userId: string,
    emoji: string
  ): Promise<Comment | null> {
    try {
      const comment = await CommentModel.findById(commentId);
      if (!comment) {
        return null;
      }

      // 检查是否已经有相同的反应
      const existingReaction = comment.reactions.find(
        r => r.user_id === userId && r.emoji === emoji
      );

      if (existingReaction) {
        // 移除现有反应
        comment.reactions = comment.reactions.filter(
          r => !(r.user_id === userId && r.emoji === emoji)
        );
      } else {
        // 添加新反应
        comment.reactions.push({
          user_id: userId,
          emoji,
          created_at: new Date(),
        });
      }

      const updatedComment = await CommentModel.update(commentId, {
        reactions: comment.reactions,
        updated_at: new Date(),
      });

      if (updatedComment) {
        // 实时推送
        await WebSocketService.broadcastToDocument(updatedComment.document_id, {
          type: WebSocketMessageType.COMMENT_UPDATED,
          data: updatedComment,
          timestamp: new Date(),
        });
      }

      return updatedComment;
    } catch (error) {
      logger.error('Failed to add reaction', { error, commentId, userId, emoji });
      throw error;
    }
  }

  /**
   * 获取文档的评论统计
   */
  static async getCommentStats(documentId: string): Promise<{
    total: number;
    active: number;
    resolved: number;
    by_author: Record<string, number>;
  }> {
    try {
      return await CommentModel.getStats(documentId);
    } catch (error) {
      logger.error('Failed to get comment stats', { error, documentId });
      throw error;
    }
  }

  /**
   * 发送提及通知
   */
  private static async sendMentionNotifications(
    comment: Comment,
    mentionedUserIds: string[]
  ): Promise<void> {
    try {
      for (const userId of mentionedUserIds) {
        if (userId !== comment.author_id) {
          await NotificationService.create({
            recipient_id: userId,
            type: NotificationType.COMMENT_MENTION,
            title: '您在评论中被提及',
            message: `${comment.author_id} 在评论中提及了您`,
            data: {
              comment_id: comment.id,
              document_id: comment.document_id,
              author_id: comment.author_id,
            },
          });
        }
      }
    } catch (error) {
      logger.error('Failed to send mention notifications', { error, comment, mentionedUserIds });
    }
  }

  /**
   * 发送回复通知
   */
  private static async sendReplyNotification(comment: Comment): Promise<void> {
    try {
      if (!comment.parent_id) return;

      const parentComment = await CommentModel.findById(comment.parent_id);
      if (!parentComment || parentComment.author_id === comment.author_id) {
        return;
      }

      await NotificationService.create({
        recipient_id: parentComment.author_id,
        type: NotificationType.COMMENT_REPLY,
        title: '您的评论收到了回复',
        message: `${comment.author_id} 回复了您的评论`,
        data: {
          comment_id: comment.id,
          parent_comment_id: comment.parent_id,
          document_id: comment.document_id,
          author_id: comment.author_id,
        },
      });
    } catch (error) {
      logger.error('Failed to send reply notification', { error, comment });
    }
  }
}
