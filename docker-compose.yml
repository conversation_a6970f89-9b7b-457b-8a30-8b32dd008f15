version: '3.8'

services:
  # 数据库服务
  postgres:
    image: postgres:15
    container_name: docmind-postgres
    environment:
      POSTGRES_DB: docmind
      POSTGRES_USER: docmind
      POSTGRES_PASSWORD: docmind123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - docmind-network

  mongodb:
    image: mongo:6.0
    container_name: docmind-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: docmind
      MONGO_INITDB_ROOT_PASSWORD: docmind123
      MONGO_INITDB_DATABASE: docmind
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - docmind-network

  redis:
    image: redis:7-alpine
    container_name: docmind-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - docmind-network

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: docmind-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - docmind-network

  minio:
    image: minio/minio:latest
    container_name: docmind-minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: docmind
      MINIO_ROOT_PASSWORD: docmind123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - docmind-network

  # 后端服务
  user-service:
    build:
      context: ./backend/user-service
      dockerfile: Dockerfile
    container_name: docmind-user-service
    environment:
      - NODE_ENV=development
      - DATABASE_URL=*********************************************/docmind
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-jwt-secret-key
    ports:
      - "3001:3000"
    depends_on:
      - postgres
      - redis
    networks:
      - docmind-network
    volumes:
      - ./backend/user-service:/app
      - /app/node_modules

  document-service:
    build:
      context: ./backend/document-service
      dockerfile: Dockerfile
    container_name: docmind-document-service
    environment:
      - NODE_ENV=development
      - DATABASE_URL=*********************************************/docmind
      - MONGODB_URL=**************************************************
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=docmind
      - MINIO_SECRET_KEY=docmind123
    ports:
      - "3002:3000"
    depends_on:
      - postgres
      - mongodb
      - minio
    networks:
      - docmind-network
    volumes:
      - ./backend/document-service:/app
      - /app/node_modules

  collaboration-service:
    build:
      context: ./backend/collaboration-service
      dockerfile: Dockerfile
    container_name: docmind-collaboration-service
    environment:
      - NODE_ENV=development
      - MONGODB_URL=**************************************************
      - REDIS_URL=redis://redis:6379
    ports:
      - "3003:3000"
    depends_on:
      - mongodb
      - redis
    networks:
      - docmind-network
    volumes:
      - ./backend/collaboration-service:/app
      - /app/node_modules

  # AI服务
  ai-service:
    build:
      context: ./ai-service
      dockerfile: Dockerfile
    container_name: docmind-ai-service
    environment:
      - PYTHONPATH=/app
      - MONGODB_URL=**************************************************
      - REDIS_URL=redis://redis:6379
      - MODEL_PATH=/app/models
    ports:
      - "8001:8000"
    depends_on:
      - mongodb
      - redis
    networks:
      - docmind-network
    volumes:
      - ./ai-service:/app
      - ai_models:/app/models
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: docmind-frontend
    environment:
      - REACT_APP_API_BASE_URL=http://localhost:8080
      - REACT_APP_WS_URL=ws://localhost:8080
    ports:
      - "3000:3000"
    networks:
      - docmind-network
    volumes:
      - ./frontend:/app
      - /app/node_modules

  # API网关
  nginx:
    image: nginx:alpine
    container_name: docmind-nginx
    ports:
      - "8080:80"
    volumes:
      - ./infrastructure/docker/nginx/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - user-service
      - document-service
      - collaboration-service
      - ai-service
      - frontend
    networks:
      - docmind-network

volumes:
  postgres_data:
  mongodb_data:
  redis_data:
  elasticsearch_data:
  minio_data:
  ai_models:

networks:
  docmind-network:
    driver: bridge
