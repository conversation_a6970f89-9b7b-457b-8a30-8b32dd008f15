import React from 'react';
import { clsx } from 'clsx';
import { LucideIcon } from 'lucide-react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  icon?: LucideIcon;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant = 'primary',
      size = 'md',
      loading = false,
      icon: Icon,
      iconPosition = 'left',
      fullWidth = false,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const baseClasses = [
      'inline-flex items-center justify-center rounded-md font-medium transition-colors',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:opacity-50 disabled:pointer-events-none',
    ];

    const variantClasses = {
      primary: [
        'bg-primary-600 text-white hover:bg-primary-700',
        'focus:ring-primary-500',
      ],
      secondary: [
        'bg-secondary-100 text-secondary-900 hover:bg-secondary-200',
        'focus:ring-secondary-500',
      ],
      outline: [
        'border border-secondary-300 bg-white text-secondary-700',
        'hover:bg-secondary-50 focus:ring-secondary-500',
      ],
      ghost: [
        'text-secondary-700 hover:bg-secondary-100',
        'focus:ring-secondary-500',
      ],
      danger: [
        'bg-danger-600 text-white hover:bg-danger-700',
        'focus:ring-danger-500',
      ],
    };

    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-sm',
      lg: 'px-6 py-3 text-base',
    };

    const iconSizeClasses = {
      sm: 'h-4 w-4',
      md: 'h-4 w-4',
      lg: 'h-5 w-5',
    };

    const classes = clsx(
      baseClasses,
      variantClasses[variant],
      sizeClasses[size],
      fullWidth && 'w-full',
      className
    );

    const iconClasses = clsx(
      iconSizeClasses[size],
      children && iconPosition === 'left' && 'mr-2',
      children && iconPosition === 'right' && 'ml-2'
    );

    const LoadingSpinner = () => (
      <svg
        className={clsx('animate-spin', iconSizeClasses[size], children && 'mr-2')}
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    );

    return (
      <button
        ref={ref}
        className={classes}
        disabled={disabled || loading}
        {...props}
      >
        {loading && <LoadingSpinner />}
        {!loading && Icon && iconPosition === 'left' && (
          <Icon className={iconClasses} />
        )}
        {children}
        {!loading && Icon && iconPosition === 'right' && (
          <Icon className={iconClasses} />
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

export default Button;
