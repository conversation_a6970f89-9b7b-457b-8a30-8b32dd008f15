import jwt from 'jsonwebtoken';
import { config } from '@/config';
import { JWTPayload, User, OrganizationRole } from '@/types/user';
import { logger } from '@/utils/logger';

export class JWTService {
  static generateAccessToken(user: User, organizationId?: string, role?: OrganizationRole): string {
    const payload: Omit<JWTPayload, 'iat' | 'exp'> = {
      sub: user.id,
      email: user.email,
      name: user.name,
      org_id: organizationId,
      role,
      iss: config.jwt.issuer,
      aud: config.jwt.audience
    };

    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn,
      issuer: config.jwt.issuer,
      audience: config.jwt.audience
    });
  }

  static generateRefreshToken(userId: string): string {
    const payload = {
      sub: userId,
      type: 'refresh',
      iss: config.jwt.issuer,
      aud: config.jwt.audience
    };

    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.refreshExpiresIn,
      issuer: config.jwt.issuer,
      audience: config.jwt.audience
    });
  }

  static verifyAccessToken(token: string): JWTPayload | null {
    try {
      const decoded = jwt.verify(token, config.jwt.secret, {
        issuer: config.jwt.issuer,
        audience: config.jwt.audience
      }) as JWTPayload;

      return decoded;
    } catch (error) {
      logger.debug('JWT verification failed:', error);
      return null;
    }
  }

  static verifyRefreshToken(token: string): { sub: string; type: string } | null {
    try {
      const decoded = jwt.verify(token, config.jwt.secret, {
        issuer: config.jwt.issuer,
        audience: config.jwt.audience
      }) as any;

      if (decoded.type !== 'refresh') {
        return null;
      }

      return decoded;
    } catch (error) {
      logger.debug('Refresh token verification failed:', error);
      return null;
    }
  }

  static decodeToken(token: string): any {
    try {
      return jwt.decode(token);
    } catch (error) {
      logger.debug('JWT decode failed:', error);
      return null;
    }
  }

  static getTokenExpiration(token: string): Date | null {
    try {
      const decoded = jwt.decode(token) as any;
      if (decoded && decoded.exp) {
        return new Date(decoded.exp * 1000);
      }
      return null;
    } catch (error) {
      logger.debug('Failed to get token expiration:', error);
      return null;
    }
  }

  static isTokenExpired(token: string): boolean {
    const expiration = this.getTokenExpiration(token);
    if (!expiration) {
      return true;
    }
    return expiration.getTime() < Date.now();
  }

  static extractTokenFromHeader(authHeader: string): string | null {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7);
  }
}
