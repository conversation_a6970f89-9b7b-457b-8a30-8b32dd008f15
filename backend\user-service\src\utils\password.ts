import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import { config } from '@/config';

export class PasswordService {
  /**
   * 哈希密码
   */
  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, config.bcrypt.rounds);
  }

  /**
   * 验证密码
   */
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  /**
   * 生成随机密码
   */
  static generateRandomPassword(length: number = 12): string {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    
    return password;
  }

  /**
   * 验证密码强度
   */
  static validatePasswordStrength(password: string): {
    isValid: boolean;
    errors: string[];
    score: number;
  } {
    const errors: string[] = [];
    let score = 0;

    // 检查最小长度
    if (password.length < config.security.passwordMinLength) {
      errors.push(`Password must be at least ${config.security.passwordMinLength} characters long`);
    } else {
      score += 1;
    }

    // 检查大写字母
    if (config.security.passwordRequireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    } else if (/[A-Z]/.test(password)) {
      score += 1;
    }

    // 检查小写字母
    if (config.security.passwordRequireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    } else if (/[a-z]/.test(password)) {
      score += 1;
    }

    // 检查数字
    if (config.security.passwordRequireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    } else if (/\d/.test(password)) {
      score += 1;
    }

    // 检查特殊字符
    if (config.security.passwordRequireSymbols && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    } else if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      score += 1;
    }

    // 额外的强度检查
    if (password.length >= 12) score += 1;
    if (password.length >= 16) score += 1;
    if (/(.)\1{2,}/.test(password)) score -= 1; // 重复字符
    if (/^(.+)\1+$/.test(password)) score -= 2; // 重复模式

    return {
      isValid: errors.length === 0,
      errors,
      score: Math.max(0, Math.min(5, score))
    };
  }

  /**
   * 生成密码重置令牌
   */
  static generateResetToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * 生成邮箱验证令牌
   */
  static generateVerificationToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * 生成安全的随机字符串
   */
  static generateSecureRandom(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * 哈希令牌（用于存储）
   */
  static hashToken(token: string): string {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  /**
   * 生成备份代码
   */
  static generateBackupCodes(count: number = 10): string[] {
    const codes: string[] = [];
    for (let i = 0; i < count; i++) {
      const code = crypto.randomBytes(4).toString('hex').toUpperCase();
      codes.push(code.match(/.{1,4}/g)?.join('-') || code);
    }
    return codes;
  }

  /**
   * 检查密码是否在常见密码列表中
   */
  static isCommonPassword(password: string): boolean {
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey',
      'dragon', 'master', 'shadow', 'superman', 'michael',
      'football', 'baseball', 'liverpool', 'jordan', 'princess'
    ];
    
    return commonPasswords.includes(password.toLowerCase());
  }

  /**
   * 生成密码哈希的盐值
   */
  static generateSalt(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * 使用PBKDF2哈希密码（备用方案）
   */
  static hashPasswordPBKDF2(password: string, salt?: string): { hash: string; salt: string } {
    const saltValue = salt || this.generateSalt();
    const hash = crypto.pbkdf2Sync(password, saltValue, 10000, 64, 'sha512').toString('hex');
    return { hash, salt: saltValue };
  }

  /**
   * 验证PBKDF2哈希的密码
   */
  static verifyPasswordPBKDF2(password: string, hash: string, salt: string): boolean {
    const hashToVerify = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
    return hash === hashToVerify;
  }
}
