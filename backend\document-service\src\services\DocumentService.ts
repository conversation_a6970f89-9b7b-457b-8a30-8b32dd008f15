import fs from 'fs-extra';
import path from 'path';
import { DocumentModel } from '@/models/Document';
import { DocumentVersionModel } from '@/models/DocumentVersion';
import { getStorageProvider } from '@/config/storage';
import { documentProcessor } from '@/utils/documentProcessor';
import { FileUtils } from '@/utils/fileUtils';
import { cacheService } from '@/config/database';
import { createError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';
import { config } from '@/config';
import {
  Document,
  CreateDocumentRequest,
  UpdateDocumentRequest,
  UploadDocumentRequest,
  DocumentSearchQuery,
  DocumentStatus,
  DocumentAnalysisResult,
  PaginationParams
} from '@/types/document';

export class DocumentService {
  /**
   * 上传文档
   */
  static async uploadDocument(
    uploadData: UploadDocumentRequest,
    userId: string
  ): Promise<Document> {
    const tempFilePath = uploadData.file.path;
    
    try {
      // 验证文件
      await this.validateFile(uploadData.file);

      // 生成唯一文件名和路径
      const uniqueFileName = FileUtils.generateUniqueFileName(uploadData.file.originalname);
      const filePath = FileUtils.generateFilePath(uploadData.organization_id, uniqueFileName);

      // 获取文件信息
      const fileInfo = await FileUtils.getFileInfo(tempFilePath);

      // 创建文档记录
      const document = await DocumentModel.create({
        title: uploadData.title,
        description: uploadData.description,
        tags: uploadData.tags,
        organization_id: uploadData.organization_id,
        file_path: filePath,
        file_name: uploadData.file.originalname,
        file_size: fileInfo.size,
        file_type: FileUtils.getFileExtension(uploadData.file.originalname),
        mime_type: fileInfo.mimeType,
        created_by: userId
      });

      // 上传文件到存储
      const storage = getStorageProvider();
      const fileBuffer = await fs.readFile(tempFilePath);
      await storage.upload(fileBuffer, filePath);

      // 异步处理文档
      this.processDocumentAsync(document.id, tempFilePath);

      // 清理临时文件
      await FileUtils.safeDeleteFile(tempFilePath);

      logger.info('Document uploaded successfully:', { 
        documentId: document.id, 
        fileName: document.file_name,
        userId 
      });

      return document;
    } catch (error) {
      // 清理临时文件
      await FileUtils.safeDeleteFile(tempFilePath);
      logger.error('Document upload failed:', error);
      throw error;
    }
  }

  /**
   * 获取文档信息
   */
  static async getDocument(id: string, userId?: string): Promise<Document | null> {
    try {
      // 先从缓存获取
      const cacheKey = `document:${id}`;
      let document = await cacheService.get<Document>(cacheKey);

      if (!document) {
        // 缓存中没有，从数据库获取
        document = await DocumentModel.findById(id);
        if (document) {
          // 缓存文档信息（30分钟）
          await cacheService.set(cacheKey, document, 1800);
        }
      }

      if (!document) {
        return null;
      }

      // TODO: 检查用户权限
      // await this.checkDocumentPermission(document, userId, 'read');

      return document;
    } catch (error) {
      logger.error('Failed to get document:', error);
      throw error;
    }
  }

  /**
   * 更新文档信息
   */
  static async updateDocument(
    id: string,
    updateData: UpdateDocumentRequest,
    userId: string
  ): Promise<Document | null> {
    try {
      const document = await DocumentModel.findById(id);
      if (!document) {
        throw createError.notFound('Document not found');
      }

      // TODO: 检查用户权限
      // await this.checkDocumentPermission(document, userId, 'write');

      const updatedDocument = await DocumentModel.update(id, updateData);
      if (!updatedDocument) {
        throw createError.notFound('Document not found');
      }

      // 更新缓存
      const cacheKey = `document:${id}`;
      await cacheService.set(cacheKey, updatedDocument, 1800);

      logger.info('Document updated successfully:', { documentId: id, userId });

      return updatedDocument;
    } catch (error) {
      logger.error('Failed to update document:', error);
      throw error;
    }
  }

  /**
   * 删除文档
   */
  static async deleteDocument(id: string, userId: string): Promise<void> {
    try {
      const document = await DocumentModel.findById(id);
      if (!document) {
        throw createError.notFound('Document not found');
      }

      // TODO: 检查用户权限
      // await this.checkDocumentPermission(document, userId, 'admin');

      // 删除存储中的文件
      const storage = getStorageProvider();
      await storage.delete(document.file_path);

      // 删除所有版本的文件
      const versions = await DocumentVersionModel.findByDocumentId(id);
      for (const version of versions) {
        await storage.delete(version.file_path);
      }

      // 删除数据库记录
      await DocumentModel.delete(id);

      // 清除缓存
      await cacheService.del(`document:${id}`);

      logger.info('Document deleted successfully:', { documentId: id, userId });
    } catch (error) {
      logger.error('Failed to delete document:', error);
      throw error;
    }
  }

  /**
   * 搜索文档
   */
  static async searchDocuments(
    searchQuery: DocumentSearchQuery,
    userId?: string
  ): Promise<{
    documents: Document[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
      has_next: boolean;
      has_prev: boolean;
    };
  }> {
    try {
      const { documents, total } = await DocumentModel.search(searchQuery);
      const { page = 1, limit = 20 } = searchQuery;
      const pages = Math.ceil(total / limit);

      return {
        documents,
        pagination: {
          page,
          limit,
          total,
          pages,
          has_next: page < pages,
          has_prev: page > 1
        }
      };
    } catch (error) {
      logger.error('Failed to search documents:', error);
      throw error;
    }
  }

  /**
   * 下载文档
   */
  static async downloadDocument(id: string, userId?: string): Promise<{
    buffer: Buffer;
    fileName: string;
    mimeType: string;
  }> {
    try {
      const document = await this.getDocument(id, userId);
      if (!document) {
        throw createError.notFound('Document not found');
      }

      // TODO: 检查下载权限
      // await this.checkDocumentPermission(document, userId, 'read');

      const storage = getStorageProvider();
      const buffer = await storage.download(document.file_path);

      logger.info('Document downloaded:', { documentId: id, userId });

      return {
        buffer,
        fileName: document.file_name,
        mimeType: document.mime_type
      };
    } catch (error) {
      logger.error('Failed to download document:', error);
      throw error;
    }
  }

  /**
   * 获取文档分析结果
   */
  static async getDocumentAnalysis(id: string, userId?: string): Promise<DocumentAnalysisResult | null> {
    try {
      const document = await this.getDocument(id, userId);
      if (!document) {
        throw createError.notFound('Document not found');
      }

      const analysisResult = await DocumentModel.getAnalysisResult(id);
      return analysisResult;
    } catch (error) {
      logger.error('Failed to get document analysis:', error);
      throw error;
    }
  }

  /**
   * 创建文档版本
   */
  static async createDocumentVersion(
    documentId: string,
    file: Express.Multer.File,
    changesSummary: string | undefined,
    userId: string
  ): Promise<Document> {
    const tempFilePath = file.path;

    try {
      const document = await DocumentModel.findById(documentId);
      if (!document) {
        throw createError.notFound('Document not found');
      }

      // TODO: 检查用户权限
      // await this.checkDocumentPermission(document, userId, 'write');

      // 验证文件
      await this.validateFile(file);

      // 生成版本文件路径
      const versionFileName = FileUtils.generateUniqueFileName(file.originalname);
      const versionFilePath = FileUtils.generateFilePath(document.organization_id, versionFileName);

      // 获取文件信息
      const fileInfo = await FileUtils.getFileInfo(tempFilePath);

      // 上传新版本文件
      const storage = getStorageProvider();
      const fileBuffer = await fs.readFile(tempFilePath);
      await storage.upload(fileBuffer, versionFilePath);

      // 创建版本记录
      await DocumentVersionModel.create({
        document_id: documentId,
        file_path: versionFilePath,
        file_size: fileInfo.size,
        changes_summary: changesSummary,
        created_by: userId
      });

      // 更新文档信息
      const updatedDocument = await DocumentModel.update(documentId, {
        title: document.title // 保持原标题，可以根据需要修改
      });

      // 异步处理新版本
      this.processDocumentAsync(documentId, tempFilePath);

      // 清理临时文件
      await FileUtils.safeDeleteFile(tempFilePath);

      logger.info('Document version created successfully:', { documentId, userId });

      return updatedDocument!;
    } catch (error) {
      await FileUtils.safeDeleteFile(tempFilePath);
      logger.error('Failed to create document version:', error);
      throw error;
    }
  }

  /**
   * 获取文档统计信息
   */
  static async getDocumentStats(organizationId?: string): Promise<any> {
    try {
      return await DocumentModel.getStats(organizationId);
    } catch (error) {
      logger.error('Failed to get document stats:', error);
      throw error;
    }
  }

  /**
   * 验证文件
   */
  private static async validateFile(file: Express.Multer.File): Promise<void> {
    // 检查文件大小
    if (file.size > config.upload.maxFileSize) {
      throw createError.payloadTooLarge(`File size exceeds maximum allowed size of ${FileUtils.formatFileSize(config.upload.maxFileSize)}`);
    }

    // 检查文件类型
    if (!config.upload.allowedMimeTypes.includes(file.mimetype)) {
      throw createError.unsupportedMediaType(`File type ${file.mimetype} is not supported`);
    }

    // 验证文件内容
    const validation = await FileUtils.validateFileType(file.path);
    if (!validation.isValid) {
      throw createError.unsupportedMediaType('File content does not match the file extension');
    }
  }

  /**
   * 异步处理文档
   */
  private static async processDocumentAsync(documentId: string, filePath: string): Promise<void> {
    try {
      // 更新状态为处理中
      await DocumentModel.updateStatus(documentId, DocumentStatus.PROCESSING);

      // 处理文档
      const analysisResult = await documentProcessor.process(filePath, { documentId });

      // 保存分析结果
      await DocumentModel.saveAnalysisResult(analysisResult);

      // 更新文档元数据
      await DocumentModel.updateMetadata(documentId, analysisResult.metadata);

      // 更新状态为完成
      await DocumentModel.updateStatus(documentId, DocumentStatus.COMPLETED);

      // 生成缩略图
      if (config.processing.thumbnailEnabled) {
        const document = await DocumentModel.findById(documentId);
        if (document) {
          const thumbnailPath = `thumbnails/${documentId}.jpg`;
          await documentProcessor.generateThumbnail(filePath, thumbnailPath);
        }
      }

      logger.info('Document processing completed:', { documentId });
    } catch (error) {
      logger.error('Document processing failed:', { documentId, error });
      await DocumentModel.updateStatus(documentId, DocumentStatus.FAILED);
    }
  }

  /**
   * 检查文档权限
   */
  private static async checkDocumentPermission(
    document: Document,
    userId: string | undefined,
    permission: 'read' | 'write' | 'admin'
  ): Promise<void> {
    // TODO: 实现权限检查逻辑
    // 1. 检查用户是否是文档创建者
    // 2. 检查用户是否有文档权限
    // 3. 检查用户是否有组织权限
    
    if (!userId) {
      throw createError.authentication('Authentication required');
    }

    // 暂时允许文档创建者访问
    if (document.created_by === userId) {
      return;
    }

    // TODO: 实现更复杂的权限检查
    throw createError.authorization('Access denied');
  }
}
