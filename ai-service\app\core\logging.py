import sys
import json
from datetime import datetime
from typing import Dict, Any
from loguru import logger
from app.config import settings

class J<PERSON>NFormatter:
    """Custom JSON formatter for structured logging"""
    
    def format(self, record: Dict[str, Any]) -> str:
        """Format log record as JSON"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record["level"].name,
            "message": record["message"],
            "service": "ai-service",
            "module": record.get("name", "unknown"),
            "function": record.get("function", "unknown"),
            "line": record.get("line", 0)
        }
        
        # Add extra fields if present
        if "extra" in record:
            log_entry.update(record["extra"])
        
        # Add exception info if present
        if record.get("exception"):
            log_entry["exception"] = {
                "type": record["exception"]["type"],
                "value": str(record["exception"]["value"]),
                "traceback": record["exception"]["traceback"]
            }
        
        return json.dumps(log_entry, ensure_ascii=False)

def setup_logging():
    """Setup logging configuration"""
    # Remove default logger
    logger.remove()
    
    # Console logging
    if settings.log_format == "json":
        logger.add(
            sys.stdout,
            format=JSONFormatter().format,
            level=settings.log_level,
            serialize=False
        )
    else:
        # Human-readable format for development
        format_string = (
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
        logger.add(
            sys.stdout,
            format=format_string,
            level=settings.log_level,
            colorize=True
        )
    
    # File logging
    if settings.log_file:
        logger.add(
            settings.log_file,
            format=JSONFormatter().format,
            level=settings.log_level,
            rotation="100 MB",
            retention="30 days",
            compression="gz",
            serialize=False
        )
    
    # Add correlation ID filter
    logger.configure(extra={"correlation_id": None})

def get_logger(name: str = None):
    """Get logger instance with optional name"""
    if name:
        return logger.bind(name=name)
    return logger

# Create module-level logger
logger = get_logger("ai-service")

# Logging utilities
def log_function_call(func_name: str, args: Dict[str, Any] = None, kwargs: Dict[str, Any] = None):
    """Log function call with parameters"""
    log_data = {"function": func_name}
    if args:
        log_data["args"] = args
    if kwargs:
        log_data["kwargs"] = kwargs
    
    logger.debug("Function called", **log_data)

def log_processing_time(func_name: str, duration: float, success: bool = True):
    """Log processing time for a function"""
    logger.info(
        f"Function {func_name} completed",
        function=func_name,
        duration=duration,
        success=success
    )

def log_model_operation(model_name: str, operation: str, success: bool = True, **kwargs):
    """Log model operations"""
    logger.info(
        f"Model operation: {operation}",
        model_name=model_name,
        operation=operation,
        success=success,
        **kwargs
    )

def log_task_status(task_id: str, status: str, **kwargs):
    """Log task status changes"""
    logger.info(
        f"Task status changed: {status}",
        task_id=task_id,
        status=status,
        **kwargs
    )

def log_error(error: Exception, context: Dict[str, Any] = None):
    """Log error with context"""
    log_data = {
        "error_type": type(error).__name__,
        "error_message": str(error)
    }
    if context:
        log_data.update(context)
    
    logger.error("Error occurred", **log_data, exc_info=True)

def log_performance_metrics(metrics: Dict[str, Any]):
    """Log performance metrics"""
    logger.info("Performance metrics", **metrics)

def log_api_request(method: str, path: str, status_code: int, duration: float, **kwargs):
    """Log API request"""
    logger.info(
        f"API request: {method} {path}",
        method=method,
        path=path,
        status_code=status_code,
        duration=duration,
        **kwargs
    )

def log_database_operation(operation: str, table: str, duration: float, success: bool = True, **kwargs):
    """Log database operations"""
    logger.info(
        f"Database operation: {operation}",
        operation=operation,
        table=table,
        duration=duration,
        success=success,
        **kwargs
    )

def log_cache_operation(operation: str, key: str, hit: bool = None, **kwargs):
    """Log cache operations"""
    log_data = {
        "operation": operation,
        "key": key
    }
    if hit is not None:
        log_data["hit"] = hit
    log_data.update(kwargs)
    
    logger.debug(f"Cache operation: {operation}", **log_data)

def log_external_service_call(service: str, endpoint: str, status_code: int, duration: float, **kwargs):
    """Log external service calls"""
    logger.info(
        f"External service call: {service}",
        service=service,
        endpoint=endpoint,
        status_code=status_code,
        duration=duration,
        **kwargs
    )

# Context managers for logging
from contextlib import contextmanager
import time

@contextmanager
def log_execution_time(operation_name: str, **context):
    """Context manager to log execution time"""
    start_time = time.time()
    try:
        logger.debug(f"Starting {operation_name}", operation=operation_name, **context)
        yield
        duration = time.time() - start_time
        logger.info(
            f"Completed {operation_name}",
            operation=operation_name,
            duration=duration,
            success=True,
            **context
        )
    except Exception as e:
        duration = time.time() - start_time
        logger.error(
            f"Failed {operation_name}",
            operation=operation_name,
            duration=duration,
            success=False,
            error=str(e),
            **context
        )
        raise

@contextmanager
def log_model_inference(model_name: str, **context):
    """Context manager to log model inference"""
    start_time = time.time()
    try:
        logger.debug(f"Starting inference with {model_name}", model=model_name, **context)
        yield
        duration = time.time() - start_time
        log_model_operation(model_name, "inference", True, duration=duration, **context)
    except Exception as e:
        duration = time.time() - start_time
        log_model_operation(model_name, "inference", False, duration=duration, error=str(e), **context)
        raise

# Decorators for automatic logging
from functools import wraps

def log_function(func):
    """Decorator to automatically log function calls"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        func_name = f"{func.__module__}.{func.__name__}"
        start_time = time.time()
        
        try:
            logger.debug(f"Calling {func_name}")
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            log_processing_time(func_name, duration, True)
            return result
        except Exception as e:
            duration = time.time() - start_time
            log_processing_time(func_name, duration, False)
            log_error(e, {"function": func_name})
            raise
    
    return wrapper

def log_async_function(func):
    """Decorator to automatically log async function calls"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        func_name = f"{func.__module__}.{func.__name__}"
        start_time = time.time()
        
        try:
            logger.debug(f"Calling async {func_name}")
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            log_processing_time(func_name, duration, True)
            return result
        except Exception as e:
            duration = time.time() - start_time
            log_processing_time(func_name, duration, False)
            log_error(e, {"function": func_name})
            raise
    
    return wrapper
