import asyncio
import time
import re
from typing import List, Dict, Any, Optional
from collections import defaultdict

from app.config import settings, CONTRACT_TEMPLATES
from app.core.models import get_model
from app.core.logging import logger, log_execution_time
from app.core.exceptions import ContractAnalysisError
from app.models.schemas import (
    ContractAnalysisRequest, ContractAnalysisResult, ContractClause, 
    ContractTerm, RiskLevel
)

class ContractAnalysisService:
    """Contract Analysis Service for intelligent contract processing"""
    
    def __init__(self):
        self.contract_templates = CONTRACT_TEMPLATES
        self.clause_patterns = self._load_clause_patterns()
        self.term_patterns = self._load_term_patterns()
    
    async def analyze_contract(self, request: ContractAnalysisRequest) -> ContractAnalysisResult:
        """Analyze contract for clauses, terms, and completeness"""
        start_time = time.time()
        
        try:
            # Initialize analysis components
            clauses = []
            terms = []
            missing_clauses = []
            recommendations = []
            
            # Detect contract type if not provided
            contract_type = request.contract_type or await self._detect_contract_type(request.text)
            
            # Extract clauses if requested
            if request.extract_clauses:
                clauses = await self._extract_clauses(request.text, contract_type)
            
            # Analyze terms if requested
            if request.analyze_terms:
                terms = await self._extract_terms(request.text)
            
            # Check completeness if requested
            completeness_score = 0.0
            if request.check_completeness:
                completeness_score, missing_clauses = await self._check_completeness(
                    request.text, contract_type, clauses
                )
            
            # Generate recommendations
            recommendations = self._generate_recommendations(clauses, terms, missing_clauses)
            
            processing_time = time.time() - start_time
            
            result = ContractAnalysisResult(
                document_id=request.document_id,
                contract_type=contract_type,
                clauses=clauses,
                terms=terms,
                completeness_score=completeness_score,
                missing_clauses=missing_clauses,
                recommendations=recommendations,
                processing_time=processing_time,
                metadata={
                    "text_length": len(request.text),
                    "clauses_found": len(clauses),
                    "terms_found": len(terms),
                    "analysis_depth": "comprehensive" if all([
                        request.extract_clauses, 
                        request.analyze_terms, 
                        request.check_completeness
                    ]) else "basic"
                }
            )
            
            logger.info(f"Contract analysis completed for document {request.document_id}",
                       extra={
                           "document_id": request.document_id,
                           "contract_type": contract_type,
                           "completeness_score": completeness_score,
                           "clauses_count": len(clauses),
                           "processing_time": processing_time
                       })
            
            return result
            
        except Exception as e:
            logger.error(f"Contract analysis failed for document {request.document_id}: {e}")
            raise ContractAnalysisError(str(e), metadata={"document_id": request.document_id})
    
    async def _detect_contract_type(self, text: str) -> Optional[str]:
        """Detect contract type based on content"""
        try:
            text_lower = text.lower()
            
            # Contract type indicators
            type_indicators = {
                "service_agreement": [
                    "service agreement", "服务协议", "service contract", "consulting agreement",
                    "professional services", "service level agreement", "sla"
                ],
                "purchase_agreement": [
                    "purchase agreement", "采购协议", "purchase order", "supply agreement",
                    "procurement contract", "buying agreement", "vendor agreement"
                ],
                "employment_contract": [
                    "employment contract", "劳动合同", "employment agreement", "job contract",
                    "work agreement", "employee contract", "hiring agreement"
                ],
                "nda": [
                    "non-disclosure agreement", "保密协议", "confidentiality agreement",
                    "nda", "secrecy agreement", "confidential information"
                ],
                "lease_agreement": [
                    "lease agreement", "租赁协议", "rental agreement", "tenancy agreement",
                    "property lease", "equipment lease"
                ],
                "partnership_agreement": [
                    "partnership agreement", "合作协议", "joint venture", "collaboration agreement",
                    "strategic partnership", "business partnership"
                ]
            }
            
            # Score each contract type
            type_scores = {}
            for contract_type, indicators in type_indicators.items():
                score = sum(1 for indicator in indicators if indicator in text_lower)
                if score > 0:
                    type_scores[contract_type] = score
            
            # Return the type with highest score
            if type_scores:
                return max(type_scores, key=type_scores.get)
            
            return "other"
            
        except Exception as e:
            logger.warning(f"Contract type detection failed: {e}")
            return "other"
    
    async def _extract_clauses(self, text: str, contract_type: str) -> List[ContractClause]:
        """Extract contract clauses"""
        try:
            clauses = []
            
            # Get clause patterns for contract type
            patterns = self.clause_patterns.get(contract_type, self.clause_patterns.get("general", {}))
            
            for clause_type, pattern_info in patterns.items():
                matches = re.finditer(pattern_info["pattern"], text, re.IGNORECASE | re.MULTILINE)
                
                for match in matches:
                    # Extract clause content (get surrounding context)
                    start = max(0, match.start() - 100)
                    end = min(len(text), match.end() + 200)
                    content = text[start:end].strip()
                    
                    # Determine importance and risk level
                    importance = pattern_info.get("importance", "standard")
                    risk_level = self._assess_clause_risk(clause_type, match.group())
                    
                    # Generate suggestions
                    suggestions = self._generate_clause_suggestions(clause_type, match.group())
                    
                    clause = ContractClause(
                        clause_type=clause_type,
                        title=pattern_info.get("title", clause_type.replace("_", " ").title()),
                        content=content,
                        location=f"Position {match.start()}-{match.end()}",
                        importance=importance,
                        risk_level=risk_level,
                        suggestions=suggestions
                    )
                    clauses.append(clause)
            
            return clauses
            
        except Exception as e:
            logger.error(f"Clause extraction failed: {e}")
            return []
    
    async def _extract_terms(self, text: str) -> List[ContractTerm]:
        """Extract contract terms"""
        try:
            terms = []
            
            for term_type, pattern_info in self.term_patterns.items():
                matches = re.finditer(pattern_info["pattern"], text, re.IGNORECASE)
                
                for match in matches:
                    # Extract value and unit
                    value = match.group(1) if match.groups() else match.group()
                    unit = pattern_info.get("unit")
                    
                    # Calculate confidence based on pattern specificity
                    confidence = pattern_info.get("confidence", 0.8)
                    
                    term = ContractTerm(
                        term_type=term_type,
                        value=value,
                        unit=unit,
                        location=f"Position {match.start()}-{match.end()}",
                        confidence=confidence
                    )
                    terms.append(term)
            
            return terms
            
        except Exception as e:
            logger.error(f"Term extraction failed: {e}")
            return []
    
    async def _check_completeness(
        self, 
        text: str, 
        contract_type: str, 
        found_clauses: List[ContractClause]
    ) -> tuple[float, List[str]]:
        """Check contract completeness"""
        try:
            template = self.contract_templates.get(contract_type)
            if not template:
                return 0.0, []
            
            required_clauses = template.get("key_clauses", [])
            found_clause_types = {clause.clause_type for clause in found_clauses}
            
            # Calculate completeness score
            found_count = len(found_clause_types.intersection(required_clauses))
            total_count = len(required_clauses)
            completeness_score = found_count / total_count if total_count > 0 else 0.0
            
            # Identify missing clauses
            missing_clauses = list(set(required_clauses) - found_clause_types)
            
            return completeness_score, missing_clauses
            
        except Exception as e:
            logger.error(f"Completeness check failed: {e}")
            return 0.0, []
    
    def _assess_clause_risk(self, clause_type: str, clause_text: str) -> RiskLevel:
        """Assess risk level of a clause"""
        high_risk_indicators = [
            "unlimited liability", "无限责任", "personal guarantee", "个人担保",
            "automatic renewal", "自动续约", "exclusive dealing", "独家交易",
            "liquidated damages", "违约金", "immediate termination", "立即终止"
        ]
        
        medium_risk_indicators = [
            "penalty", "罚金", "termination for convenience", "便利终止",
            "intellectual property assignment", "知识产权转让",
            "confidentiality", "保密", "non-compete", "竞业禁止"
        ]
        
        clause_lower = clause_text.lower()
        
        # Check for high risk indicators
        if any(indicator in clause_lower for indicator in high_risk_indicators):
            return RiskLevel.HIGH
        
        # Check for medium risk indicators
        if any(indicator in clause_lower for indicator in medium_risk_indicators):
            return RiskLevel.MEDIUM
        
        # Default to low risk
        return RiskLevel.LOW
    
    def _generate_clause_suggestions(self, clause_type: str, clause_text: str) -> List[str]:
        """Generate suggestions for clause improvement"""
        suggestions = []
        
        suggestion_rules = {
            "payment_terms": [
                "Consider adding late payment penalties",
                "Specify payment methods and currency",
                "Include dispute resolution for payment issues"
            ],
            "liability": [
                "Consider limiting liability to contract value",
                "Add mutual indemnification clauses",
                "Exclude consequential damages"
            ],
            "termination": [
                "Add notice period requirements",
                "Specify termination procedures",
                "Include data return obligations"
            ],
            "intellectual_property": [
                "Clarify ownership of work products",
                "Add IP indemnification clauses",
                "Specify license terms"
            ]
        }
        
        return suggestion_rules.get(clause_type, ["Review clause for completeness and clarity"])
    
    def _generate_recommendations(
        self, 
        clauses: List[ContractClause], 
        terms: List[ContractTerm], 
        missing_clauses: List[str]
    ) -> List[str]:
        """Generate overall contract recommendations"""
        recommendations = []
        
        # High-risk clause recommendations
        high_risk_clauses = [c for c in clauses if c.risk_level == RiskLevel.HIGH]
        if high_risk_clauses:
            recommendations.append(f"Review {len(high_risk_clauses)} high-risk clauses carefully")
        
        # Missing clause recommendations
        if missing_clauses:
            recommendations.append(f"Consider adding missing clauses: {', '.join(missing_clauses)}")
        
        # Term-specific recommendations
        payment_terms = [t for t in terms if t.term_type == "payment_amount"]
        if not payment_terms:
            recommendations.append("Payment terms are not clearly specified")
        
        # General recommendations
        if len(clauses) < 5:
            recommendations.append("Contract appears to be incomplete - consider adding more detailed clauses")
        
        if not recommendations:
            recommendations.append("Contract appears to be well-structured")
        
        return recommendations
    
    def _load_clause_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Load clause detection patterns"""
        return {
            "service_agreement": {
                "service_scope": {
                    "pattern": r"(scope of (work|services?)|工作范围|服务范围).{0,200}",
                    "title": "Service Scope",
                    "importance": "critical"
                },
                "payment_terms": {
                    "pattern": r"(payment|付款|费用).{0,200}",
                    "title": "Payment Terms",
                    "importance": "critical"
                },
                "service_level": {
                    "pattern": r"(service level|服务水平|performance standard|绩效标准).{0,200}",
                    "title": "Service Level Agreement",
                    "importance": "important"
                }
            },
            "general": {
                "liability": {
                    "pattern": r"(liability|责任|indemnif|赔偿).{0,200}",
                    "title": "Liability",
                    "importance": "critical"
                },
                "termination": {
                    "pattern": r"(terminat|终止|expir|到期).{0,200}",
                    "title": "Termination",
                    "importance": "important"
                },
                "confidentiality": {
                    "pattern": r"(confidential|保密|non.?disclosure|不披露).{0,200}",
                    "title": "Confidentiality",
                    "importance": "important"
                }
            }
        }
    
    def _load_term_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Load term extraction patterns"""
        return {
            "payment_amount": {
                "pattern": r"([￥$€£]\s*[\d,]+(?:\.\d{2})?|\d+(?:,\d{3})*(?:\.\d{2})?\s*(?:yuan|dollars?|euros?|pounds?))",
                "unit": "currency",
                "confidence": 0.9
            },
            "duration": {
                "pattern": r"(\d+)\s*(years?|months?|days?|weeks?|年|月|日|周)",
                "unit": "time",
                "confidence": 0.8
            },
            "percentage": {
                "pattern": r"(\d+(?:\.\d+)?)\s*%",
                "unit": "percent",
                "confidence": 0.9
            },
            "date": {
                "pattern": r"(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?|\d{1,2}[-/]\d{1,2}[-/]\d{4})",
                "unit": "date",
                "confidence": 0.8
            }
        }

# Global contract analysis service instance
contract_analysis_service = ContractAnalysisService()
