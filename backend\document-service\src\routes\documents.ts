import express from 'express';
import multer from 'multer';
import { DocumentController } from '../controllers/DocumentController';
import { authMiddleware } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';
import { uploadDocumentSchema, updateDocumentSchema, searchDocumentSchema } from '../utils/validation';

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/temp/',
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB
  },
  fileFilter: (req, file, cb) => {
    const allowedMimeTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'image/jpeg',
      'image/png',
      'image/tiff',
    ];
    
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Unsupported file type'));
    }
  },
});

/**
 * @swagger
 * /api/documents:
 *   get:
 *     summary: Get documents list
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [uploading, processing, completed, failed, archived]
 *     responses:
 *       200:
 *         description: Documents list retrieved successfully
 */
router.get('/', authMiddleware, validateRequest(searchDocumentSchema), DocumentController.getDocuments);

/**
 * @swagger
 * /api/documents/search:
 *   get:
 *     summary: Search documents
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: query
 *         schema:
 *           type: string
 *         required: true
 *     responses:
 *       200:
 *         description: Search results retrieved successfully
 */
router.get('/search', authMiddleware, validateRequest(searchDocumentSchema), DocumentController.searchDocuments);

/**
 * @swagger
 * /api/documents/stats:
 *   get:
 *     summary: Get document statistics
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Document statistics retrieved successfully
 */
router.get('/stats', authMiddleware, DocumentController.getDocumentStats);

/**
 * @swagger
 * /api/documents/upload:
 *   post:
 *     summary: Upload a document
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               organization_id:
 *                 type: string
 *             required:
 *               - file
 *               - title
 *               - organization_id
 *     responses:
 *       201:
 *         description: Document uploaded successfully
 */
router.post('/upload', authMiddleware, upload.single('file'), validateRequest(uploadDocumentSchema), DocumentController.uploadDocument);

/**
 * @swagger
 * /api/documents/bulk/delete:
 *   post:
 *     summary: Bulk delete documents
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               document_ids:
 *                 type: array
 *                 items:
 *                   type: string
 *             required:
 *               - document_ids
 *     responses:
 *       200:
 *         description: Documents deleted successfully
 */
router.post('/bulk/delete', authMiddleware, DocumentController.bulkDeleteDocuments);

/**
 * @swagger
 * /api/documents/{id}:
 *   get:
 *     summary: Get document by ID
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Document retrieved successfully
 *       404:
 *         description: Document not found
 */
router.get('/:id', authMiddleware, DocumentController.getDocumentById);

/**
 * @swagger
 * /api/documents/{id}:
 *   put:
 *     summary: Update document
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Document updated successfully
 */
router.put('/:id', authMiddleware, validateRequest(updateDocumentSchema), DocumentController.updateDocument);

/**
 * @swagger
 * /api/documents/{id}:
 *   delete:
 *     summary: Delete document
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Document deleted successfully
 */
router.delete('/:id', authMiddleware, DocumentController.deleteDocument);

/**
 * @swagger
 * /api/documents/{id}/download:
 *   get:
 *     summary: Download document
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Document file
 *         content:
 *           application/octet-stream:
 *             schema:
 *               type: string
 *               format: binary
 */
router.get('/:id/download', authMiddleware, DocumentController.downloadDocument);

/**
 * @swagger
 * /api/documents/{id}/analysis:
 *   get:
 *     summary: Get document analysis result
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Document analysis result retrieved successfully
 */
router.get('/:id/analysis', authMiddleware, DocumentController.getDocumentAnalysis);

/**
 * @swagger
 * /api/documents/{id}/versions:
 *   get:
 *     summary: Get document versions
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Document versions retrieved successfully
 */
router.get('/:id/versions', authMiddleware, DocumentController.getDocumentVersions);

/**
 * @swagger
 * /api/documents/{id}/versions:
 *   post:
 *     summary: Create document version
 *     tags: [Documents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *               changes_summary:
 *                 type: string
 *             required:
 *               - file
 *     responses:
 *       201:
 *         description: Document version created successfully
 */
router.post('/:id/versions', authMiddleware, upload.single('file'), DocumentController.createDocumentVersion);

export default router;
