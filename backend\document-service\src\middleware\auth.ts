import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '@/config';
import { createError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';

// 扩展Request接口
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        name: string;
        org_id?: string;
        role?: string;
      };
    }
  }
}

interface JWTPayload {
  sub: string;
  email: string;
  name: string;
  org_id?: string;
  role?: string;
  iat: number;
  exp: number;
  iss: string;
  aud: string;
}

/**
 * JWT认证中间件
 */
export function authenticateToken(req: Request, res: Response, next: NextFunction): void {
  const authHeader = req.headers.authorization;
  const token = extractTokenFromHeader(authHeader || '');

  if (!token) {
    throw createError.authentication('Access token is required');
  }

  try {
    const payload = jwt.verify(token, config.security.jwtSecret, {
      issuer: 'docmind',
      audience: 'docmind-users'
    }) as JWTPayload;

    req.user = {
      id: payload.sub,
      email: payload.email,
      name: payload.name,
      org_id: payload.org_id,
      role: payload.role
    };

    next();
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw createError.authentication('Token expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw createError.authentication('Invalid token');
    } else {
      throw createError.authentication('Authentication failed');
    }
  }
}

/**
 * 可选认证中间件（不强制要求token）
 */
export function optionalAuth(req: Request, res: Response, next: NextFunction): void {
  const authHeader = req.headers.authorization;
  const token = extractTokenFromHeader(authHeader || '');

  if (token) {
    try {
      const payload = jwt.verify(token, config.security.jwtSecret, {
        issuer: 'docmind',
        audience: 'docmind-users'
      }) as JWTPayload;

      req.user = {
        id: payload.sub,
        email: payload.email,
        name: payload.name,
        org_id: payload.org_id,
        role: payload.role
      };
    } catch (error) {
      // 可选认证失败时不抛出错误，只记录日志
      logger.debug('Optional authentication failed:', error);
    }
  }

  next();
}

/**
 * 角色权限检查中间件工厂
 */
export function requireRole(...roles: string[]) {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw createError.authentication('User not authenticated');
    }

    if (!req.user.role) {
      throw createError.authorization('User role not specified');
    }

    if (!roles.includes(req.user.role)) {
      throw createError.authorization(`Access denied. Required roles: ${roles.join(', ')}`);
    }

    next();
  };
}

/**
 * 组织成员检查中间件
 */
export function requireOrganizationMember(req: Request, res: Response, next: NextFunction): void {
  if (!req.user) {
    throw createError.authentication('User not authenticated');
  }

  if (!req.user.org_id) {
    throw createError.authorization('User is not a member of any organization');
  }

  next();
}

/**
 * 组织权限检查中间件
 */
export function requireOrganizationAccess(req: Request, res: Response, next: NextFunction): void {
  if (!req.user) {
    throw createError.authentication('User not authenticated');
  }

  const organizationId = req.params.organizationId || req.body.organization_id || req.query.organization_id;
  
  if (!organizationId) {
    throw createError.validation('Organization ID is required');
  }

  if (req.user.org_id !== organizationId && !['owner', 'admin'].includes(req.user.role || '')) {
    throw createError.authorization('Access denied to this organization');
  }

  next();
}

/**
 * 文档权限检查中间件工厂
 */
export function requireDocumentPermission(permission: 'read' | 'write' | 'admin') {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    if (!req.user) {
      throw createError.authentication('User not authenticated');
    }

    const documentId = req.params.id || req.params.documentId;
    
    if (!documentId) {
      throw createError.validation('Document ID is required');
    }

    try {
      // TODO: 实现文档权限检查逻辑
      // 1. 查询文档信息
      // 2. 检查用户是否有相应权限
      // 3. 检查组织权限
      
      // 暂时允许所有已认证用户访问
      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * API密钥认证中间件（用于服务间调用）
 */
export function authenticateApiKey(req: Request, res: Response, next: NextFunction): void {
  const apiKey = req.headers['x-api-key'] as string;
  
  if (!apiKey) {
    throw createError.authentication('API key is required');
  }

  // 这里应该验证API密钥的有效性
  const validApiKeys = process.env.VALID_API_KEYS?.split(',') || [];
  
  if (!validApiKeys.includes(apiKey)) {
    throw createError.authentication('Invalid API key');
  }

  next();
}

/**
 * 文件上传权限检查
 */
export function requireUploadPermission(req: Request, res: Response, next: NextFunction): void {
  if (!req.user) {
    throw createError.authentication('User not authenticated');
  }

  // 检查用户是否有上传权限
  if (!['owner', 'admin', 'manager', 'member'].includes(req.user.role || '')) {
    throw createError.authorization('Insufficient permissions to upload files');
  }

  next();
}

/**
 * 审计日志中间件
 */
export function auditLog(action: string, resourceType: string) {
  return (req: Request, res: Response, next: NextFunction): void => {
    // 在响应完成后记录审计日志
    res.on('finish', () => {
      if (req.user && res.statusCode < 400) {
        // 异步记录审计日志
        setImmediate(async () => {
          try {
            logger.info('Audit log:', {
              user_id: req.user?.id,
              action,
              resource_type: resourceType,
              resource_id: req.params.id,
              ip_address: req.ip,
              user_agent: req.get('User-Agent'),
              method: req.method,
              path: req.path,
              status_code: res.statusCode,
              organization_id: req.user?.org_id
            });
          } catch (error) {
            logger.error('Failed to record audit log:', error);
          }
        });
      }
    });

    next();
  };
}

/**
 * 限流中间件工厂
 */
export function createRateLimit(windowMs: number, max: number, message?: string) {
  const requests = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction): void => {
    const key = req.ip || 'unknown';
    const now = Date.now();
    const windowStart = now - windowMs;

    // 清理过期的记录
    for (const [ip, data] of requests.entries()) {
      if (data.resetTime < now) {
        requests.delete(ip);
      }
    }

    const current = requests.get(key);
    
    if (!current) {
      requests.set(key, { count: 1, resetTime: now + windowMs });
      next();
      return;
    }

    if (current.count >= max) {
      throw createError.rateLimit(message || 'Too many requests');
    }

    current.count++;
    next();
  };
}

/**
 * 从请求头中提取token
 */
function extractTokenFromHeader(authHeader: string): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}
