import { Request, Response } from 'express';
import { UserService } from '@/services/UserService';
import { asyncHandler } from '@/middleware/errorHandler';
import { ApiResponse, PaginatedResponse, PublicUser } from '@/types/user';

export class UserController {
  /**
   * 获取用户列表
   */
  static getUsers = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const result = await UserService.getUsers(req.query as any);
    
    const response: ApiResponse<PaginatedResponse<PublicUser>> = {
      success: true,
      data: {
        data: result.users,
        pagination: result.pagination
      },
      message: 'Users retrieved successfully'
    };

    res.json(response);
  });

  /**
   * 根据ID获取用户
   */
  static getUserById = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const user = await UserService.getUserById(id);
    
    if (!user) {
      const response: ApiResponse = {
        success: false,
        message: 'User not found'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse<PublicUser> = {
      success: true,
      data: user,
      message: 'User retrieved successfully'
    };

    res.json(response);
  });

  /**
   * 更新用户
   */
  static updateUser = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const user = await UserService.updateUser(id, req.body);
    
    const response: ApiResponse<PublicUser> = {
      success: true,
      data: user,
      message: 'User updated successfully'
    };

    res.json(response);
  });

  /**
   * 删除用户
   */
  static deleteUser = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    await UserService.deleteUser(id);
    
    const response: ApiResponse = {
      success: true,
      message: 'User deleted successfully'
    };

    res.json(response);
  });

  /**
   * 获取用户统计信息
   */
  static getUserStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const stats = await UserService.getUserStats();
    
    const response: ApiResponse = {
      success: true,
      data: stats,
      message: 'User statistics retrieved successfully'
    };

    res.json(response);
  });

  /**
   * 批量删除用户
   */
  static bulkDeleteUsers = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { userIds } = req.body;
    
    if (!Array.isArray(userIds) || userIds.length === 0) {
      const response: ApiResponse = {
        success: false,
        message: 'User IDs array is required'
      };
      return res.status(400).json(response);
    }

    // TODO: 实现批量删除逻辑
    const deletedCount = userIds.length; // 临时值
    
    const response: ApiResponse = {
      success: true,
      data: { deletedCount },
      message: `${deletedCount} users deleted successfully`
    };

    res.json(response);
  });

  /**
   * 搜索用户
   */
  static searchUsers = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { q: search, ...otherParams } = req.query;
    
    const result = await UserService.getUsers({
      ...otherParams,
      search: search as string
    } as any);
    
    const response: ApiResponse<PaginatedResponse<PublicUser>> = {
      success: true,
      data: {
        data: result.users,
        pagination: result.pagination
      },
      message: 'Search results retrieved successfully'
    };

    res.json(response);
  });

  /**
   * 导出用户数据
   */
  static exportUsers = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    // TODO: 实现用户数据导出逻辑
    // 1. 获取用户数据
    // 2. 转换为CSV或Excel格式
    // 3. 返回文件下载
    
    const response: ApiResponse = {
      success: true,
      message: 'Export functionality not implemented yet'
    };

    res.json(response);
  });

  /**
   * 重置用户密码（管理员功能）
   */
  static resetUserPassword = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    
    // TODO: 实现管理员重置用户密码逻辑
    // 1. 生成临时密码
    // 2. 更新用户密码
    // 3. 发送新密码给用户
    
    const response: ApiResponse = {
      success: true,
      message: 'Password reset successfully'
    };

    res.json(response);
  });

  /**
   * 激活/停用用户
   */
  static toggleUserStatus = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const { status } = req.body;
    
    // TODO: 实现用户状态切换逻辑
    
    const response: ApiResponse = {
      success: true,
      message: `User ${status} successfully`
    };

    res.json(response);
  });
}
