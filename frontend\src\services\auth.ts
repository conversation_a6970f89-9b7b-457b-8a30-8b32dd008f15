import ApiService from './api';
import { User, LoginForm, RegisterForm, ApiResponse } from '@/types';

export interface LoginResponse {
  user: User;
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export interface RefreshTokenResponse {
  access_token: string;
  expires_in: number;
}

export class AuthService {
  // 登录
  static async login(credentials: LoginForm): Promise<LoginResponse> {
    const response = await ApiService.post<LoginResponse>('/api/users/auth/login', credentials);
    
    if (response.success && response.data) {
      // 保存token
      localStorage.setItem('access_token', response.data.access_token);
      localStorage.setItem('refresh_token', response.data.refresh_token);
      
      // 保存用户信息
      localStorage.setItem('user', JSON.stringify(response.data.user));
      
      // 设置token过期时间
      const expiresAt = Date.now() + response.data.expires_in * 1000;
      localStorage.setItem('token_expires_at', expiresAt.toString());
      
      return response.data;
    }
    
    throw new Error(response.message || '登录失败');
  }

  // 注册
  static async register(userData: RegisterForm): Promise<LoginResponse> {
    const response = await ApiService.post<LoginResponse>('/api/users/auth/register', userData);
    
    if (response.success && response.data) {
      // 保存token
      localStorage.setItem('access_token', response.data.access_token);
      localStorage.setItem('refresh_token', response.data.refresh_token);
      
      // 保存用户信息
      localStorage.setItem('user', JSON.stringify(response.data.user));
      
      // 设置token过期时间
      const expiresAt = Date.now() + response.data.expires_in * 1000;
      localStorage.setItem('token_expires_at', expiresAt.toString());
      
      return response.data;
    }
    
    throw new Error(response.message || '注册失败');
  }

  // 登出
  static async logout(): Promise<void> {
    try {
      await ApiService.post('/api/users/auth/logout');
    } catch (error) {
      // 即使服务器登出失败，也要清除本地数据
      console.error('Server logout failed:', error);
    } finally {
      // 清除本地存储
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user');
      localStorage.removeItem('token_expires_at');
    }
  }

  // 刷新token
  static async refreshToken(): Promise<string> {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await ApiService.post<RefreshTokenResponse>('/api/users/auth/refresh', {
      refresh_token: refreshToken,
    });

    if (response.success && response.data) {
      // 更新access token
      localStorage.setItem('access_token', response.data.access_token);
      
      // 更新过期时间
      const expiresAt = Date.now() + response.data.expires_in * 1000;
      localStorage.setItem('token_expires_at', expiresAt.toString());
      
      return response.data.access_token;
    }

    throw new Error(response.message || 'Token refresh failed');
  }

  // 获取当前用户信息
  static async getCurrentUser(): Promise<User> {
    const response = await ApiService.get<User>('/api/users/auth/profile');
    
    if (response.success && response.data) {
      // 更新本地用户信息
      localStorage.setItem('user', JSON.stringify(response.data));
      return response.data;
    }
    
    throw new Error(response.message || '获取用户信息失败');
  }

  // 更新用户信息
  static async updateProfile(userData: Partial<User>): Promise<User> {
    const response = await ApiService.put<User>('/api/users/auth/profile', userData);
    
    if (response.success && response.data) {
      // 更新本地用户信息
      localStorage.setItem('user', JSON.stringify(response.data));
      return response.data;
    }
    
    throw new Error(response.message || '更新用户信息失败');
  }

  // 修改密码
  static async changePassword(data: {
    current_password: string;
    new_password: string;
  }): Promise<void> {
    const response = await ApiService.post('/api/users/auth/change-password', data);
    
    if (!response.success) {
      throw new Error(response.message || '密码修改失败');
    }
  }

  // 忘记密码
  static async forgotPassword(email: string): Promise<void> {
    const response = await ApiService.post('/api/users/auth/forgot-password', { email });
    
    if (!response.success) {
      throw new Error(response.message || '发送重置邮件失败');
    }
  }

  // 重置密码
  static async resetPassword(data: {
    token: string;
    new_password: string;
  }): Promise<void> {
    const response = await ApiService.post('/api/users/auth/reset-password', data);
    
    if (!response.success) {
      throw new Error(response.message || '密码重置失败');
    }
  }

  // 发送邮箱验证
  static async sendEmailVerification(): Promise<void> {
    const response = await ApiService.post('/api/users/auth/send-verification');
    
    if (!response.success) {
      throw new Error(response.message || '发送验证邮件失败');
    }
  }

  // 验证邮箱
  static async verifyEmail(token: string): Promise<void> {
    const response = await ApiService.post('/api/users/auth/verify-email', { token });
    
    if (!response.success) {
      throw new Error(response.message || '邮箱验证失败');
    }
  }

  // 检查是否已登录
  static isAuthenticated(): boolean {
    const token = localStorage.getItem('access_token');
    const expiresAt = localStorage.getItem('token_expires_at');
    
    if (!token || !expiresAt) {
      return false;
    }
    
    // 检查token是否过期
    return Date.now() < parseInt(expiresAt);
  }

  // 获取本地存储的用户信息
  static getStoredUser(): User | null {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch (error) {
        console.error('Failed to parse stored user:', error);
        localStorage.removeItem('user');
      }
    }
    return null;
  }

  // 获取访问token
  static getAccessToken(): string | null {
    return localStorage.getItem('access_token');
  }

  // 获取刷新token
  static getRefreshToken(): string | null {
    return localStorage.getItem('refresh_token');
  }

  // 检查token是否即将过期（5分钟内）
  static isTokenExpiringSoon(): boolean {
    const expiresAt = localStorage.getItem('token_expires_at');
    if (!expiresAt) {
      return true;
    }
    
    const expirationTime = parseInt(expiresAt);
    const fiveMinutesFromNow = Date.now() + 5 * 60 * 1000;
    
    return expirationTime <= fiveMinutesFromNow;
  }

  // 自动刷新token
  static async autoRefreshToken(): Promise<void> {
    if (this.isTokenExpiringSoon() && this.getRefreshToken()) {
      try {
        await this.refreshToken();
      } catch (error) {
        console.error('Auto refresh token failed:', error);
        // 如果刷新失败，清除所有认证信息
        await this.logout();
        throw error;
      }
    }
  }

  // 设置自动刷新定时器
  static setupAutoRefresh(): void {
    // 每分钟检查一次token状态
    setInterval(() => {
      if (this.isAuthenticated()) {
        this.autoRefreshToken().catch(console.error);
      }
    }, 60 * 1000);
  }
}

export default AuthService;
