import { Express } from 'express';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { config } from '@/config';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'DocMind User Service API',
      version: config.app.version,
      description: 'DocMind 用户服务 API 文档',
      contact: {
        name: 'DocMind Team',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: `http://localhost:${config.app.port}`,
        description: '开发环境'
      },
      {
        url: 'https://api.docmind.com',
        description: '生产环境'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      },
      schemas: {
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              format: 'uuid',
              description: '用户ID'
            },
            email: {
              type: 'string',
              format: 'email',
              description: '邮箱地址'
            },
            name: {
              type: 'string',
              description: '用户姓名'
            },
            avatar_url: {
              type: 'string',
              format: 'uri',
              description: '头像URL'
            },
            phone: {
              type: 'string',
              description: '手机号码'
            },
            status: {
              type: 'string',
              enum: ['active', 'inactive', 'suspended'],
              description: '用户状态'
            },
            email_verified: {
              type: 'boolean',
              description: '邮箱是否已验证'
            },
            last_login_at: {
              type: 'string',
              format: 'date-time',
              description: '最后登录时间'
            },
            created_at: {
              type: 'string',
              format: 'date-time',
              description: '创建时间'
            },
            updated_at: {
              type: 'string',
              format: 'date-time',
              description: '更新时间'
            }
          }
        },
        LoginResponse: {
          type: 'object',
          properties: {
            user: {
              $ref: '#/components/schemas/User'
            },
            access_token: {
              type: 'string',
              description: '访问令牌'
            },
            refresh_token: {
              type: 'string',
              description: '刷新令牌'
            },
            expires_in: {
              type: 'integer',
              description: '令牌过期时间（秒）'
            }
          }
        },
        ApiResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              description: '请求是否成功'
            },
            data: {
              description: '响应数据'
            },
            message: {
              type: 'string',
              description: '响应消息'
            },
            error: {
              type: 'string',
              description: '错误信息'
            },
            errors: {
              type: 'object',
              description: '验证错误详情'
            }
          }
        },
        PaginatedResponse: {
          type: 'object',
          properties: {
            data: {
              type: 'array',
              description: '数据列表'
            },
            pagination: {
              type: 'object',
              properties: {
                page: {
                  type: 'integer',
                  description: '当前页码'
                },
                limit: {
                  type: 'integer',
                  description: '每页数量'
                },
                total: {
                  type: 'integer',
                  description: '总数量'
                },
                pages: {
                  type: 'integer',
                  description: '总页数'
                },
                has_next: {
                  type: 'boolean',
                  description: '是否有下一页'
                },
                has_prev: {
                  type: 'boolean',
                  description: '是否有上一页'
                }
              }
            }
          }
        },
        Error: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false
            },
            message: {
              type: 'string',
              description: '错误消息'
            },
            code: {
              type: 'string',
              description: '错误代码'
            },
            timestamp: {
              type: 'string',
              format: 'date-time',
              description: '错误时间'
            },
            path: {
              type: 'string',
              description: '请求路径'
            },
            method: {
              type: 'string',
              description: '请求方法'
            }
          }
        }
      },
      responses: {
        UnauthorizedError: {
          description: '认证失败',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        ForbiddenError: {
          description: '权限不足',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        NotFoundError: {
          description: '资源不存在',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        ValidationError: {
          description: '验证失败',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        InternalServerError: {
          description: '服务器内部错误',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        }
      }
    },
    tags: [
      {
        name: 'Authentication',
        description: '用户认证相关接口'
      },
      {
        name: 'Users',
        description: '用户管理相关接口'
      },
      {
        name: 'Organizations',
        description: '组织管理相关接口'
      },
      {
        name: 'Health',
        description: '健康检查相关接口'
      }
    ]
  },
  apis: [
    './src/routes/*.ts',
    './src/controllers/*.ts'
  ]
};

const specs = swaggerJsdoc(options);

export function swaggerSetup(app: Express): void {
  // Swagger UI
  app.use('/api/docs', swaggerUi.serve, swaggerUi.setup(specs, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'DocMind User Service API'
  }));

  // JSON格式的API文档
  app.get('/api/docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(specs);
  });
}
