import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';
import { config } from '@/config';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
  code?: string;
}

export class CustomError extends Error implements AppError {
  public statusCode: number;
  public isOperational: boolean;
  public code?: string;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true, code?: string) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.code = code;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends CustomError {
  constructor(message: string = 'Validation failed') {
    super(message, 400, true, 'VALIDATION_ERROR');
  }
}

export class AuthenticationError extends CustomError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401, true, 'AUTHENTICATION_ERROR');
  }
}

export class AuthorizationError extends CustomError {
  constructor(message: string = 'Access denied') {
    super(message, 403, true, 'AUTHORIZATION_ERROR');
  }
}

export class NotFoundError extends CustomError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, true, 'NOT_FOUND_ERROR');
  }
}

export class ConflictError extends CustomError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409, true, 'CONFLICT_ERROR');
  }
}

export class PayloadTooLargeError extends CustomError {
  constructor(message: string = 'File too large') {
    super(message, 413, true, 'PAYLOAD_TOO_LARGE');
  }
}

export class UnsupportedMediaTypeError extends CustomError {
  constructor(message: string = 'Unsupported file type') {
    super(message, 415, true, 'UNSUPPORTED_MEDIA_TYPE');
  }
}

export class RateLimitError extends CustomError {
  constructor(message: string = 'Too many requests') {
    super(message, 429, true, 'RATE_LIMIT_ERROR');
  }
}

export class ProcessingError extends CustomError {
  constructor(message: string = 'Document processing failed') {
    super(message, 422, true, 'PROCESSING_ERROR');
  }
}

export class StorageError extends CustomError {
  constructor(message: string = 'Storage operation failed') {
    super(message, 500, true, 'STORAGE_ERROR');
  }
}

export class DatabaseError extends CustomError {
  constructor(message: string = 'Database error') {
    super(message, 500, true, 'DATABASE_ERROR');
  }
}

export class ExternalServiceError extends CustomError {
  constructor(message: string = 'External service error') {
    super(message, 502, true, 'EXTERNAL_SERVICE_ERROR');
  }
}

// 错误处理中间件
export function errorHandler(
  error: AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  let { statusCode = 500, message, code } = error;

  // 处理特定类型的错误
  if (error.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation failed';
    code = 'VALIDATION_ERROR';
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Invalid token';
    code = 'INVALID_TOKEN';
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Token expired';
    code = 'TOKEN_EXPIRED';
  } else if (error.name === 'CastError') {
    statusCode = 400;
    message = 'Invalid ID format';
    code = 'INVALID_ID';
  } else if (error.code === '23505') { // PostgreSQL unique violation
    statusCode = 409;
    message = 'Resource already exists';
    code = 'DUPLICATE_RESOURCE';
  } else if (error.code === '23503') { // PostgreSQL foreign key violation
    statusCode = 400;
    message = 'Referenced resource does not exist';
    code = 'INVALID_REFERENCE';
  } else if (error.code === 'ECONNREFUSED') {
    statusCode = 503;
    message = 'Service unavailable';
    code = 'SERVICE_UNAVAILABLE';
  } else if (error.code === 'ENOENT') {
    statusCode = 404;
    message = 'File not found';
    code = 'FILE_NOT_FOUND';
  } else if (error.code === 'ENOSPC') {
    statusCode = 507;
    message = 'Insufficient storage space';
    code = 'INSUFFICIENT_STORAGE';
  } else if (error.code === 'LIMIT_FILE_SIZE') {
    statusCode = 413;
    message = 'File too large';
    code = 'FILE_TOO_LARGE';
  } else if (error.code === 'LIMIT_UNEXPECTED_FILE') {
    statusCode = 400;
    message = 'Unexpected file field';
    code = 'UNEXPECTED_FILE';
  }

  // 记录错误日志
  const logLevel = statusCode >= 500 ? 'error' : 'warn';
  logger[logLevel]('Request error:', {
    error: {
      message: error.message,
      stack: error.stack,
      code: error.code,
      statusCode
    },
    request: {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
      params: req.params,
      query: req.query,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    }
  });

  // 构建错误响应
  const errorResponse: any = {
    success: false,
    message,
    code,
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method
  };

  // 在开发环境下包含错误堆栈
  if (config.app.env === 'development') {
    errorResponse.stack = error.stack;
    errorResponse.details = error;
  }

  // 发送错误响应
  res.status(statusCode).json(errorResponse);
}

// 异步错误处理包装器
export function asyncHandler(fn: Function) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

// 404错误处理
export function notFoundHandler(req: Request, res: Response, next: NextFunction): void {
  const error = new NotFoundError(`Route ${req.method} ${req.path} not found`);
  next(error);
}

// 全局未捕获异常处理
export function setupGlobalErrorHandlers(): void {
  process.on('uncaughtException', (error: Error) => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
  });

  process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
  });
}

// 错误工厂函数
export const createError = {
  validation: (message?: string) => new ValidationError(message),
  authentication: (message?: string) => new AuthenticationError(message),
  authorization: (message?: string) => new AuthorizationError(message),
  notFound: (message?: string) => new NotFoundError(message),
  conflict: (message?: string) => new ConflictError(message),
  payloadTooLarge: (message?: string) => new PayloadTooLargeError(message),
  unsupportedMediaType: (message?: string) => new UnsupportedMediaTypeError(message),
  rateLimit: (message?: string) => new RateLimitError(message),
  processing: (message?: string) => new ProcessingError(message),
  storage: (message?: string) => new StorageError(message),
  database: (message?: string) => new DatabaseError(message),
  externalService: (message?: string) => new ExternalServiceError(message),
  custom: (message: string, statusCode: number, code?: string) => 
    new CustomError(message, statusCode, true, code)
};
