{"name": "docmind-collaboration-service", "version": "1.0.0", "description": "DocMind Collaboration Service - 协作服务", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write \"src/**/*.{ts,js,json}\"", "type-check": "tsc --noEmit", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:studio": "npx prisma studio", "db:seed": "ts-node prisma/seed.ts"}, "keywords": ["collaboration", "comments", "approval", "workflow", "typescript", "express"], "author": "DocMind Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "joi": "^17.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "nodemailer": "^6.9.7", "ioredis": "^5.3.2", "pg": "^8.11.3", "mongodb": "^6.3.0", "socket.io": "^4.7.4", "winston": "^3.11.0", "express-winston": "^4.2.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "express-validator": "^7.0.1", "express-async-errors": "^3.1.1", "http-status-codes": "^2.3.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "axios": "^1.6.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.9.0", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/uuid": "^9.0.7", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/pg": "^8.10.9", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/lodash": "^4.14.202", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.2.2", "ts-node": "^10.9.1", "nodemon": "^3.0.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.53.0", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "prettier": "^3.1.0", "prisma": "^5.6.0", "@prisma/client": "^5.6.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}