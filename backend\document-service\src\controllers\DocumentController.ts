import { Request, Response } from 'express';
import { DocumentService } from '@/services/DocumentService';
import { asyncHandler } from '@/middleware/errorHandler';
import { ApiResponse, PaginatedResponse, Document } from '@/types/document';

export class DocumentController {
  /**
   * 上传文档
   */
  static uploadDocument = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const uploadData = {
      ...req.body,
      file: req.file
    };

    const document = await DocumentService.uploadDocument(uploadData, req.user.id);

    const response: ApiResponse<Document> = {
      success: true,
      data: document,
      message: 'Document uploaded successfully'
    };

    res.status(201).json(response);
  });

  /**
   * 获取文档列表
   */
  static getDocuments = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const searchQuery = req.query as any;
    const result = await DocumentService.searchDocuments(searchQuery, req.user?.id);

    const response: ApiResponse<PaginatedResponse<Document>> = {
      success: true,
      data: {
        data: result.documents,
        pagination: result.pagination
      },
      message: 'Documents retrieved successfully'
    };

    res.json(response);
  });

  /**
   * 根据ID获取文档
   */
  static getDocumentById = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const document = await DocumentService.getDocument(id, req.user?.id);

    if (!document) {
      const response: ApiResponse = {
        success: false,
        message: 'Document not found'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse<Document> = {
      success: true,
      data: document,
      message: 'Document retrieved successfully'
    };

    res.json(response);
  });

  /**
   * 更新文档
   */
  static updateDocument = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const { id } = req.params;
    const document = await DocumentService.updateDocument(id, req.body, req.user.id);

    const response: ApiResponse<Document> = {
      success: true,
      data: document,
      message: 'Document updated successfully'
    };

    res.json(response);
  });

  /**
   * 删除文档
   */
  static deleteDocument = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const { id } = req.params;
    await DocumentService.deleteDocument(id, req.user.id);

    const response: ApiResponse = {
      success: true,
      message: 'Document deleted successfully'
    };

    res.json(response);
  });

  /**
   * 下载文档
   */
  static downloadDocument = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const { buffer, fileName, mimeType } = await DocumentService.downloadDocument(id, req.user?.id);

    res.setHeader('Content-Type', mimeType);
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    res.setHeader('Content-Length', buffer.length);

    res.send(buffer);
  });

  /**
   * 搜索文档
   */
  static searchDocuments = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const searchQuery = req.query as any;
    const result = await DocumentService.searchDocuments(searchQuery, req.user?.id);

    const response: ApiResponse<PaginatedResponse<Document>> = {
      success: true,
      data: {
        data: result.documents,
        pagination: result.pagination
      },
      message: 'Search results retrieved successfully'
    };

    res.json(response);
  });

  /**
   * 获取文档分析结果
   */
  static getDocumentAnalysis = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    const analysisResult = await DocumentService.getDocumentAnalysis(id, req.user?.id);

    if (!analysisResult) {
      const response: ApiResponse = {
        success: false,
        message: 'Document analysis not found or not completed'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true,
      data: analysisResult,
      message: 'Document analysis retrieved successfully'
    };

    res.json(response);
  });

  /**
   * 创建文档版本
   */
  static createDocumentVersion = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const { id } = req.params;
    const { changes_summary } = req.body;

    const document = await DocumentService.createDocumentVersion(
      id,
      req.file,
      changes_summary,
      req.user.id
    );

    const response: ApiResponse<Document> = {
      success: true,
      data: document,
      message: 'Document version created successfully'
    };

    res.status(201).json(response);
  });

  /**
   * 获取文档统计信息
   */
  static getDocumentStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { organization_id } = req.query;
    const stats = await DocumentService.getDocumentStats(organization_id as string);

    const response: ApiResponse = {
      success: true,
      data: stats,
      message: 'Document statistics retrieved successfully'
    };

    res.json(response);
  });

  /**
   * 批量删除文档
   */
  static bulkDeleteDocuments = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const { document_ids } = req.body;

    if (!Array.isArray(document_ids) || document_ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Document IDs array is required'
      });
    }

    let deletedCount = 0;
    const errors: string[] = [];

    for (const documentId of document_ids) {
      try {
        await DocumentService.deleteDocument(documentId, req.user.id);
        deletedCount++;
      } catch (error) {
        errors.push(`Failed to delete document ${documentId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    const response: ApiResponse = {
      success: errors.length === 0,
      data: { 
        deleted_count: deletedCount,
        total_requested: document_ids.length,
        errors: errors.length > 0 ? errors : undefined
      },
      message: `${deletedCount} documents deleted successfully`
    };

    res.json(response);
  });

  /**
   * 获取文档预览
   */
  static getDocumentPreview = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    
    // TODO: 实现文档预览逻辑
    // 1. 获取文档信息
    // 2. 生成预览内容（缩略图、文本摘要等）
    // 3. 返回预览数据

    const response: ApiResponse = {
      success: true,
      message: 'Document preview functionality not implemented yet'
    };

    res.json(response);
  });

  /**
   * 获取文档缩略图
   */
  static getDocumentThumbnail = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { id } = req.params;
    
    // TODO: 实现缩略图获取逻辑
    // 1. 检查缩略图是否存在
    // 2. 返回缩略图文件

    const response: ApiResponse = {
      success: true,
      message: 'Document thumbnail functionality not implemented yet'
    };

    res.json(response);
  });
}
