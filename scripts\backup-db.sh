#!/bin/bash

# DocMind 数据库备份脚本

set -e

# 配置
BACKUP_DIR="backup"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
POSTGRES_BACKUP_FILE="postgres_backup_${TIMESTAMP}.sql"
MONGODB_BACKUP_DIR="mongodb_backup_${TIMESTAMP}"

# 创建备份目录
mkdir -p $BACKUP_DIR

echo "🗄️  开始数据库备份..."

# 备份 PostgreSQL
echo "备份 PostgreSQL 数据库..."
docker-compose exec -T postgres pg_dump -U docmind docmind > "$BACKUP_DIR/$POSTGRES_BACKUP_FILE"
if [ $? -eq 0 ]; then
    echo "✅ PostgreSQL 备份完成: $BACKUP_DIR/$POSTGRES_BACKUP_FILE"
else
    echo "❌ PostgreSQL 备份失败"
    exit 1
fi

# 备份 MongoDB
echo "备份 MongoDB 数据库..."
docker-compose exec -T mongodb mongodump --uri="****************************************************" --out="/tmp/$MONGODB_BACKUP_DIR"
docker-compose exec -T mongodb tar -czf "/tmp/${MONGODB_BACKUP_DIR}.tar.gz" -C "/tmp" "$MONGODB_BACKUP_DIR"
docker cp "$(docker-compose ps -q mongodb):/tmp/${MONGODB_BACKUP_DIR}.tar.gz" "$BACKUP_DIR/"
if [ $? -eq 0 ]; then
    echo "✅ MongoDB 备份完成: $BACKUP_DIR/${MONGODB_BACKUP_DIR}.tar.gz"
else
    echo "❌ MongoDB 备份失败"
    exit 1
fi

# 压缩备份文件
echo "压缩备份文件..."
tar -czf "$BACKUP_DIR/docmind_backup_${TIMESTAMP}.tar.gz" -C "$BACKUP_DIR" "$POSTGRES_BACKUP_FILE" "${MONGODB_BACKUP_DIR}.tar.gz"
if [ $? -eq 0 ]; then
    echo "✅ 备份压缩完成: $BACKUP_DIR/docmind_backup_${TIMESTAMP}.tar.gz"
    # 删除临时文件
    rm "$BACKUP_DIR/$POSTGRES_BACKUP_FILE" "$BACKUP_DIR/${MONGODB_BACKUP_DIR}.tar.gz"
else
    echo "❌ 备份压缩失败"
fi

# 清理旧备份（保留最近7天）
echo "清理旧备份文件..."
find $BACKUP_DIR -name "docmind_backup_*.tar.gz" -mtime +7 -delete
echo "✅ 备份完成！"

echo ""
echo "备份文件位置: $BACKUP_DIR/docmind_backup_${TIMESTAMP}.tar.gz"
