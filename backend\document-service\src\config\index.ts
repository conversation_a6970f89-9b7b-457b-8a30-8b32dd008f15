import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../../.env') });

export const config = {
  app: {
    name: process.env.APP_NAME || 'DocMind Document Service',
    version: process.env.APP_VERSION || '1.0.0',
    env: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.APP_PORT || '3000', 10),
    host: process.env.APP_HOST || '0.0.0.0'
  },

  database: {
    postgres: {
      url: process.env.DATABASE_URL || 'postgresql://docmind:docmind123@localhost:5432/docmind',
      pool: {
        min: parseInt(process.env.DB_POOL_MIN || '2', 10),
        max: parseInt(process.env.DB_POOL_MAX || '10', 10),
        idle: parseInt(process.env.DB_POOL_IDLE || '10000', 10)
      }
    },
    mongodb: {
      url: process.env.MONGODB_URL || '****************************************************',
      options: {
        maxPoolSize: parseInt(process.env.MONGO_POOL_SIZE || '10', 10),
        serverSelectionTimeoutMS: parseInt(process.env.MONGO_TIMEOUT || '5000', 10)
      }
    }
  },

  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    prefix: process.env.CACHE_PREFIX || 'docmind:doc:',
    ttl: parseInt(process.env.CACHE_TTL || '3600', 10)
  },

  storage: {
    provider: process.env.STORAGE_PROVIDER || 'minio',
    minio: {
      endpoint: process.env.MINIO_ENDPOINT || 'localhost:9000',
      accessKey: process.env.MINIO_ACCESS_KEY || 'docmind',
      secretKey: process.env.MINIO_SECRET_KEY || 'docmind123',
      bucket: process.env.MINIO_BUCKET || 'docmind-documents',
      useSSL: process.env.MINIO_USE_SSL === 'true',
      region: process.env.MINIO_REGION || 'us-east-1'
    },
    local: {
      uploadPath: process.env.LOCAL_UPLOAD_PATH || 'uploads',
      thumbnailPath: process.env.LOCAL_THUMBNAIL_PATH || 'uploads/thumbnails'
    }
  },

  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '104857600', 10), // 100MB
    allowedMimeTypes: (process.env.ALLOWED_MIME_TYPES || 
      'application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/plain,image/jpeg,image/png'
    ).split(','),
    allowedExtensions: (process.env.ALLOWED_EXTENSIONS || 
      'pdf,doc,docx,txt,jpg,jpeg,png'
    ).split(','),
    tempPath: process.env.TEMP_UPLOAD_PATH || 'temp',
    chunkSize: parseInt(process.env.UPLOAD_CHUNK_SIZE || '1048576', 10), // 1MB
    maxConcurrentUploads: parseInt(process.env.MAX_CONCURRENT_UPLOADS || '5', 10)
  },

  processing: {
    enabled: process.env.PROCESSING_ENABLED !== 'false',
    aiServiceUrl: process.env.AI_SERVICE_URL || 'http://localhost:8001',
    aiServiceApiKey: process.env.AI_SERVICE_API_KEY || '',
    ocrEnabled: process.env.OCR_ENABLED !== 'false',
    thumbnailEnabled: process.env.THUMBNAIL_ENABLED !== 'false',
    thumbnailSize: {
      width: parseInt(process.env.THUMBNAIL_WIDTH || '200', 10),
      height: parseInt(process.env.THUMBNAIL_HEIGHT || '300', 10)
    },
    maxProcessingTime: parseInt(process.env.MAX_PROCESSING_TIME || '300000', 10), // 5分钟
    retryAttempts: parseInt(process.env.PROCESSING_RETRY_ATTEMPTS || '3', 10)
  },

  search: {
    enabled: process.env.SEARCH_ENABLED !== 'false',
    elasticsearch: {
      url: process.env.ELASTICSEARCH_URL || 'http://localhost:9200',
      index: process.env.ELASTICSEARCH_INDEX || 'docmind-documents',
      maxResults: parseInt(process.env.SEARCH_MAX_RESULTS || '100', 10)
    }
  },

  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    credentials: process.env.CORS_CREDENTIALS === 'true'
  },

  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW || '900000', 10), // 15分钟
    max: parseInt(process.env.RATE_LIMIT_MAX || '100', 10),
    uploadWindowMs: parseInt(process.env.UPLOAD_RATE_LIMIT_WINDOW || '3600000', 10), // 1小时
    uploadMax: parseInt(process.env.UPLOAD_RATE_LIMIT_MAX || '50', 10)
  },

  security: {
    jwtSecret: process.env.JWT_SECRET || 'your-jwt-secret',
    encryptionKey: process.env.ENCRYPTION_KEY || 'your-32-character-encryption-key',
    virusScanEnabled: process.env.VIRUS_SCAN_ENABLED === 'true',
    quarantinePath: process.env.QUARANTINE_PATH || 'quarantine'
  },

  log: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'combined',
    file: process.env.LOG_FILE || 'logs/document-service.log'
  },

  monitoring: {
    healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000', 10),
    metricsEnabled: process.env.METRICS_ENABLED === 'true',
    prometheusPort: parseInt(process.env.PROMETHEUS_PORT || '9091', 10)
  },

  cleanup: {
    enabled: process.env.CLEANUP_ENABLED !== 'false',
    tempFileMaxAge: parseInt(process.env.TEMP_FILE_MAX_AGE || '86400000', 10), // 24小时
    deletedFileRetention: parseInt(process.env.DELETED_FILE_RETENTION || '**********', 10), // 30天
    cleanupInterval: process.env.CLEANUP_INTERVAL || '0 2 * * *' // 每天凌晨2点
  },

  backup: {
    enabled: process.env.BACKUP_ENABLED === 'true',
    schedule: process.env.BACKUP_SCHEDULE || '0 3 * * *', // 每天凌晨3点
    retentionDays: parseInt(process.env.BACKUP_RETENTION_DAYS || '30', 10),
    destination: process.env.BACKUP_DESTINATION || 'backups'
  },

  notification: {
    enabled: process.env.NOTIFICATION_ENABLED !== 'false',
    webhookUrl: process.env.NOTIFICATION_WEBHOOK_URL || '',
    emailEnabled: process.env.EMAIL_NOTIFICATION_ENABLED === 'true'
  }
};

// 验证必需的环境变量
const requiredEnvVars = [
  'DATABASE_URL',
  'MONGODB_URL',
  'JWT_SECRET'
];

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Required environment variable ${envVar} is not set`);
  }
}

export default config;
