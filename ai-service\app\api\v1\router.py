from fastapi import APIRouter
from .endpoints import ocr, nlp, risk_analysis, contract_analysis, tasks, models, health

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(
    health.router,
    prefix="/health",
    tags=["health"]
)

api_router.include_router(
    ocr.router,
    prefix="/ocr",
    tags=["ocr"]
)

api_router.include_router(
    nlp.router,
    prefix="/nlp",
    tags=["nlp"]
)

api_router.include_router(
    risk_analysis.router,
    prefix="/risk-analysis",
    tags=["risk-analysis"]
)

api_router.include_router(
    contract_analysis.router,
    prefix="/contract-analysis",
    tags=["contract-analysis"]
)

api_router.include_router(
    tasks.router,
    prefix="/tasks",
    tags=["tasks"]
)

api_router.include_router(
    models.router,
    prefix="/models",
    tags=["models"]
)
