# DocMind Makefile

.PHONY: help setup dev build test clean docker-build docker-up docker-down k8s-deploy

# 默认目标
help:
	@echo "DocMind 开发工具"
	@echo ""
	@echo "可用命令:"
	@echo "  setup          - 安装所有依赖"
	@echo "  dev            - 启动开发环境"
	@echo "  build          - 构建所有服务"
	@echo "  test           - 运行所有测试"
	@echo "  lint           - 代码检查"
	@echo "  clean          - 清理构建文件"
	@echo "  docker-build   - 构建Docker镜像"
	@echo "  docker-up      - 启动Docker服务"
	@echo "  docker-down    - 停止Docker服务"
	@echo "  k8s-deploy     - 部署到Kubernetes"
	@echo "  db-init        - 初始化数据库"
	@echo "  db-migrate     - 运行数据库迁移"
	@echo "  logs           - 查看服务日志"

# 安装依赖
setup:
	@echo "安装项目依赖..."
	npm install
	@echo "安装前端依赖..."
	cd frontend && npm install
	@echo "安装后端依赖..."
	cd backend/user-service && npm install
	cd backend/document-service && npm install
	cd backend/collaboration-service && npm install
	cd backend/notification-service && npm install
	cd backend/search-service && npm install
	@echo "安装AI服务依赖..."
	cd ai-service && pip install -r requirements.txt
	@echo "依赖安装完成!"

# 启动开发环境
dev:
	@echo "启动开发环境..."
	docker-compose up -d postgres mongodb redis elasticsearch minio
	@echo "等待数据库启动..."
	sleep 10
	@echo "启动应用服务..."
	npm run dev

# 构建所有服务
build:
	@echo "构建前端..."
	cd frontend && npm run build
	@echo "构建后端服务..."
	cd backend/user-service && npm run build
	cd backend/document-service && npm run build
	cd backend/collaboration-service && npm run build
	@echo "构建完成!"

# 运行测试
test:
	@echo "运行前端测试..."
	cd frontend && npm test -- --watchAll=false
	@echo "运行后端测试..."
	cd backend/user-service && npm test
	cd backend/document-service && npm test
	cd backend/collaboration-service && npm test
	@echo "运行AI服务测试..."
	cd ai-service && python -m pytest
	@echo "测试完成!"

# 代码检查
lint:
	@echo "检查前端代码..."
	cd frontend && npm run lint
	@echo "检查后端代码..."
	cd backend/user-service && npm run lint
	cd backend/document-service && npm run lint
	cd backend/collaboration-service && npm run lint
	@echo "检查AI服务代码..."
	cd ai-service && flake8 . && black --check .
	@echo "代码检查完成!"

# 清理构建文件
clean:
	@echo "清理构建文件..."
	rm -rf frontend/build
	rm -rf frontend/dist
	rm -rf backend/*/dist
	rm -rf backend/*/build
	rm -rf ai-service/__pycache__
	rm -rf ai-service/**/__pycache__
	@echo "清理完成!"

# Docker相关命令
docker-build:
	@echo "构建Docker镜像..."
	docker-compose build

docker-up:
	@echo "启动Docker服务..."
	docker-compose up -d

docker-down:
	@echo "停止Docker服务..."
	docker-compose down

docker-logs:
	@echo "查看Docker服务日志..."
	docker-compose logs -f

# Kubernetes部署
k8s-deploy:
	@echo "部署到Kubernetes..."
	kubectl apply -f infrastructure/kubernetes/namespace.yaml
	kubectl apply -f infrastructure/kubernetes/configmap.yaml
	kubectl apply -f infrastructure/kubernetes/secret.yaml
	kubectl apply -f infrastructure/kubernetes/database/
	kubectl apply -f infrastructure/kubernetes/backend/
	kubectl apply -f infrastructure/kubernetes/ai-service/
	kubectl apply -f infrastructure/kubernetes/frontend/
	kubectl apply -f infrastructure/kubernetes/ingress.yaml
	@echo "部署完成!"

# 数据库相关命令
db-init:
	@echo "初始化数据库..."
	docker-compose exec postgres psql -U docmind -d docmind -f /docker-entrypoint-initdb.d/init.sql
	@echo "数据库初始化完成!"

db-migrate:
	@echo "运行数据库迁移..."
	cd backend/user-service && npm run migrate
	cd backend/document-service && npm run migrate
	@echo "数据库迁移完成!"

db-seed:
	@echo "填充测试数据..."
	cd backend/user-service && npm run seed
	cd backend/document-service && npm run seed
	@echo "测试数据填充完成!"

# 日志查看
logs:
	@echo "查看应用日志..."
	docker-compose logs -f --tail=100

logs-frontend:
	docker-compose logs -f frontend

logs-backend:
	docker-compose logs -f user-service document-service collaboration-service

logs-ai:
	docker-compose logs -f ai-service

# 健康检查
health:
	@echo "检查服务健康状态..."
	@curl -f http://localhost:3001/health || echo "用户服务异常"
	@curl -f http://localhost:3002/health || echo "文档服务异常"
	@curl -f http://localhost:3003/health || echo "协作服务异常"
	@curl -f http://localhost:8001/health || echo "AI服务异常"
	@curl -f http://localhost:3000 || echo "前端服务异常"

# 重启服务
restart:
	@echo "重启所有服务..."
	docker-compose restart

restart-backend:
	@echo "重启后端服务..."
	docker-compose restart user-service document-service collaboration-service

restart-ai:
	@echo "重启AI服务..."
	docker-compose restart ai-service

# 备份数据
backup:
	@echo "备份数据库..."
	docker-compose exec postgres pg_dump -U docmind docmind > backup/postgres_$(shell date +%Y%m%d_%H%M%S).sql
	docker-compose exec mongodb mongodump --uri="****************************************************" --out=backup/mongodb_$(shell date +%Y%m%d_%H%M%S)
	@echo "备份完成!"

# 性能测试
perf-test:
	@echo "运行性能测试..."
	cd scripts && python performance_test.py
	@echo "性能测试完成!"

# 安全扫描
security-scan:
	@echo "运行安全扫描..."
	npm audit
	cd frontend && npm audit
	cd backend/user-service && npm audit
	cd ai-service && safety check
	@echo "安全扫描完成!"
