# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
pydantic==2.5.0
pydantic-settings==2.1.0

# AI and ML libraries
torch==2.1.1
torchvision==0.16.1
transformers==4.36.0
sentence-transformers==2.2.2
spacy==3.7.2
nltk==3.8.1
scikit-learn==1.3.2
numpy==1.24.4
pandas==2.1.4

# OCR libraries
pytesseract==0.3.10
opencv-python==********
Pillow==10.1.0
pdf2image==1.16.3

# Document processing
PyPDF2==3.0.1
python-docx==1.1.0
openpyxl==3.1.2
python-pptx==0.6.23

# Database and storage
pymongo==4.6.0
redis==5.0.1
psycopg2-binary==2.9.9
sqlalchemy==2.0.23

# HTTP and API clients
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# Utilities
python-dotenv==1.0.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-magic==0.4.27
chardet==5.2.0

# Logging and monitoring
loguru==0.7.2
prometheus-client==0.19.0

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Chinese language support
jieba==0.42.1
zhconv==1.4.3

# Additional ML libraries
langchain==0.0.350
openai==1.3.7
anthropic==0.7.8

# File format support
textract==1.6.5
pdfplumber==0.10.3
docx2txt==0.8

# Image processing
imageio==2.33.0
matplotlib==3.8.2

# Async support
asyncio==3.4.3
aiofiles==23.2.1

# Configuration
pyyaml==6.0.1
toml==0.10.2

# Security
cryptography==41.0.8
bcrypt==4.1.2

# Data validation
marshmallow==3.20.1
cerberus==1.3.5

# Task queue (optional)
celery==5.3.4
redis==5.0.1

# Model serving
onnxruntime==1.16.3
optimum==1.16.0
