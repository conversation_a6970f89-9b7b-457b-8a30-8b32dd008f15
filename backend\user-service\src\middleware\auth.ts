import { Request, Response, NextFunction } from 'express';
import { JWTService } from '@/utils/jwt';
import { UserModel } from '@/models/User';
import { cacheService } from '@/config/redis';
import { createError } from '@/middleware/errorHandler';
import { JWTPayload, OrganizationRole } from '@/types/user';
import { logger } from '@/utils/logger';

// 扩展Request接口
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        name: string;
        org_id?: string;
        role?: OrganizationRole;
      };
    }
  }
}

/**
 * JWT认证中间件
 */
export function authenticateToken(req: Request, res: Response, next: NextFunction): void {
  const authHeader = req.headers.authorization;
  const token = JWTService.extractTokenFromHeader(authHeader || '');

  if (!token) {
    throw createError.authentication('Access token is required');
  }

  const payload = JWTService.verifyAccessToken(token);
  if (!payload) {
    throw createError.authentication('Invalid or expired token');
  }

  req.user = {
    id: payload.sub,
    email: payload.email,
    name: payload.name,
    org_id: payload.org_id,
    role: payload.role
  };

  next();
}

/**
 * 可选认证中间件（不强制要求token）
 */
export function optionalAuth(req: Request, res: Response, next: NextFunction): void {
  const authHeader = req.headers.authorization;
  const token = JWTService.extractTokenFromHeader(authHeader || '');

  if (token) {
    const payload = JWTService.verifyAccessToken(token);
    if (payload) {
      req.user = {
        id: payload.sub,
        email: payload.email,
        name: payload.name,
        org_id: payload.org_id,
        role: payload.role
      };
    }
  }

  next();
}

/**
 * 验证用户是否存在且状态正常
 */
export async function validateUser(req: Request, res: Response, next: NextFunction): Promise<void> {
  if (!req.user) {
    throw createError.authentication('User not authenticated');
  }

  try {
    // 先从缓存中查找用户
    const cacheKey = `user:${req.user.id}`;
    let user = await cacheService.get(cacheKey);

    if (!user) {
      // 缓存中没有，从数据库查找
      user = await UserModel.findById(req.user.id);
      if (user) {
        // 缓存用户信息（5分钟）
        await cacheService.set(cacheKey, user, 300);
      }
    }

    if (!user) {
      throw createError.authentication('User not found');
    }

    if (user.status !== 'active') {
      throw createError.authentication('User account is not active');
    }

    next();
  } catch (error) {
    next(error);
  }
}

/**
 * 角色权限检查中间件工厂
 */
export function requireRole(...roles: OrganizationRole[]) {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw createError.authentication('User not authenticated');
    }

    if (!req.user.role) {
      throw createError.authorization('User role not specified');
    }

    if (!roles.includes(req.user.role)) {
      throw createError.authorization(`Access denied. Required roles: ${roles.join(', ')}`);
    }

    next();
  };
}

/**
 * 组织成员检查中间件
 */
export function requireOrganizationMember(req: Request, res: Response, next: NextFunction): void {
  if (!req.user) {
    throw createError.authentication('User not authenticated');
  }

  if (!req.user.org_id) {
    throw createError.authorization('User is not a member of any organization');
  }

  next();
}

/**
 * 资源所有者检查中间件
 */
export function requireResourceOwner(resourceUserIdField: string = 'userId') {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw createError.authentication('User not authenticated');
    }

    const resourceUserId = req.params[resourceUserIdField] || req.body[resourceUserIdField];
    
    if (!resourceUserId) {
      throw createError.validation(`${resourceUserIdField} is required`);
    }

    if (req.user.id !== resourceUserId) {
      // 检查是否是管理员角色
      if (!req.user.role || !['owner', 'admin'].includes(req.user.role)) {
        throw createError.authorization('Access denied. You can only access your own resources');
      }
    }

    next();
  };
}

/**
 * API密钥认证中间件（用于服务间调用）
 */
export function authenticateApiKey(req: Request, res: Response, next: NextFunction): void {
  const apiKey = req.headers['x-api-key'] as string;
  
  if (!apiKey) {
    throw createError.authentication('API key is required');
  }

  // 这里应该验证API密钥的有效性
  // 可以从数据库或配置中验证
  const validApiKeys = process.env.VALID_API_KEYS?.split(',') || [];
  
  if (!validApiKeys.includes(apiKey)) {
    throw createError.authentication('Invalid API key');
  }

  next();
}

/**
 * 限制登录尝试中间件
 */
export function rateLimitLogin(req: Request, res: Response, next: NextFunction): void {
  // 这个中间件会在具体的登录路由中实现
  // 这里只是占位符
  next();
}

/**
 * 检查邮箱是否已验证
 */
export async function requireEmailVerified(req: Request, res: Response, next: NextFunction): Promise<void> {
  if (!req.user) {
    throw createError.authentication('User not authenticated');
  }

  try {
    const user = await UserModel.findById(req.user.id);
    
    if (!user) {
      throw createError.authentication('User not found');
    }

    if (!user.email_verified) {
      throw createError.authorization('Email verification required');
    }

    next();
  } catch (error) {
    next(error);
  }
}

/**
 * 审计日志中间件
 */
export function auditLog(action: string, resourceType: string) {
  return (req: Request, res: Response, next: NextFunction): void => {
    // 在响应完成后记录审计日志
    res.on('finish', () => {
      if (req.user && res.statusCode < 400) {
        // 异步记录审计日志
        setImmediate(async () => {
          try {
            // 这里会调用审计日志服务
            logger.info('Audit log:', {
              user_id: req.user?.id,
              action,
              resource_type: resourceType,
              resource_id: req.params.id,
              ip_address: req.ip,
              user_agent: req.get('User-Agent'),
              method: req.method,
              path: req.path,
              status_code: res.statusCode
            });
          } catch (error) {
            logger.error('Failed to record audit log:', error);
          }
        });
      }
    });

    next();
  };
}
