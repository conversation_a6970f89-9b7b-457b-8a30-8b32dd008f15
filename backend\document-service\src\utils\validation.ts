import Joi from 'joi';
import { config } from '@/config';

// 基础验证规则
const uuidSchema = Joi.string()
  .uuid()
  .required()
  .messages({
    'string.uuid': 'Invalid ID format',
    'any.required': 'ID is required'
  });

const titleSchema = Joi.string()
  .min(1)
  .max(500)
  .required()
  .messages({
    'string.min': 'Title is required',
    'string.max': 'Title must not exceed 500 characters',
    'any.required': 'Title is required'
  });

const descriptionSchema = Joi.string()
  .max(2000)
  .optional()
  .allow('')
  .messages({
    'string.max': 'Description must not exceed 2000 characters'
  });

const tagsSchema = Joi.array()
  .items(Joi.string().max(50))
  .max(20)
  .optional()
  .messages({
    'array.max': 'Maximum 20 tags allowed',
    'string.max': 'Tag must not exceed 50 characters'
  });

// 文档创建验证
export const createDocumentSchema = Joi.object({
  title: titleSchema,
  description: descriptionSchema,
  tags: tagsSchema,
  organization_id: uuidSchema
});

// 文档更新验证
export const updateDocumentSchema = Joi.object({
  title: Joi.string().min(1).max(500).optional(),
  description: descriptionSchema,
  tags: tagsSchema
});

// 文档搜索验证
export const documentSearchSchema = Joi.object({
  query: Joi.string().max(200).optional(),
  tags: Joi.array().items(Joi.string().max(50)).max(10).optional(),
  file_type: Joi.string().max(50).optional(),
  status: Joi.string().valid('uploading', 'processing', 'completed', 'failed', 'archived').optional(),
  created_by: uuidSchema.optional(),
  organization_id: uuidSchema.optional(),
  date_from: Joi.date().optional(),
  date_to: Joi.date().optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  sort: Joi.string().valid('created_at', 'updated_at', 'title', 'file_size').default('created_at'),
  order: Joi.string().valid('asc', 'desc').default('desc')
});

// 文档分享验证
export const createShareSchema = Joi.object({
  document_id: uuidSchema,
  shared_with: Joi.string().email().optional(),
  permissions: Joi.array()
    .items(Joi.string().valid('read', 'write', 'admin'))
    .min(1)
    .required()
    .messages({
      'array.min': 'At least one permission is required'
    }),
  expires_at: Joi.date().greater('now').optional(),
  password: Joi.string().min(6).max(50).optional(),
  download_allowed: Joi.boolean().default(true)
});

// 评论创建验证
export const createCommentSchema = Joi.object({
  document_id: uuidSchema,
  content: Joi.string().min(1).max(1000).required().messages({
    'string.min': 'Comment content is required',
    'string.max': 'Comment must not exceed 1000 characters'
  }),
  page_number: Joi.number().integer().min(1).optional(),
  position: Joi.object({
    x: Joi.number().required(),
    y: Joi.number().required()
  }).optional(),
  parent_id: uuidSchema.optional()
});

// 评论更新验证
export const updateCommentSchema = Joi.object({
  content: Joi.string().min(1).max(1000).required().messages({
    'string.min': 'Comment content is required',
    'string.max': 'Comment must not exceed 1000 characters'
  })
});

// 文档权限验证
export const documentPermissionSchema = Joi.object({
  user_id: uuidSchema,
  permission: Joi.string().valid('read', 'write', 'admin').required()
});

// 批量操作验证
export const bulkOperationSchema = Joi.object({
  document_ids: Joi.array()
    .items(uuidSchema)
    .min(1)
    .max(100)
    .required()
    .messages({
      'array.min': 'At least one document ID is required',
      'array.max': 'Maximum 100 documents allowed per operation'
    }),
  action: Joi.string().valid('delete', 'archive', 'restore').required()
});

// 分页参数验证
export const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  sort: Joi.string().valid('created_at', 'updated_at', 'title', 'file_size', 'file_type').default('created_at'),
  order: Joi.string().valid('asc', 'desc').default('desc')
});

// ID参数验证
export const idParamSchema = Joi.object({
  id: uuidSchema
});

// 文件上传验证
export const fileUploadSchema = Joi.object({
  title: titleSchema,
  description: descriptionSchema,
  tags: tagsSchema,
  organization_id: uuidSchema
});

// 版本创建验证
export const createVersionSchema = Joi.object({
  changes_summary: Joi.string().max(500).optional()
});

// 统计查询验证
export const statsQuerySchema = Joi.object({
  organization_id: uuidSchema.optional(),
  date_from: Joi.date().optional(),
  date_to: Joi.date().optional(),
  group_by: Joi.string().valid('day', 'week', 'month', 'year').default('month')
});

// 验证中间件工厂函数
export function validateBody(schema: Joi.ObjectSchema) {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors: Record<string, string[]> = {};
      error.details.forEach((detail) => {
        const field = detail.path.join('.');
        if (!errors[field]) {
          errors[field] = [];
        }
        errors[field].push(detail.message);
      });

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors
      });
    }

    req.body = value;
    next();
  };
}

export function validateQuery(schema: Joi.ObjectSchema) {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors: Record<string, string[]> = {};
      error.details.forEach((detail) => {
        const field = detail.path.join('.');
        if (!errors[field]) {
          errors[field] = [];
        }
        errors[field].push(detail.message);
      });

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors
      });
    }

    req.query = value;
    next();
  };
}

export function validateParams(schema: Joi.ObjectSchema) {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.params, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors: Record<string, string[]> = {};
      error.details.forEach((detail) => {
        const field = detail.path.join('.');
        if (!errors[field]) {
          errors[field] = [];
        }
        errors[field].push(detail.message);
      });

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors
      });
    }

    req.params = value;
    next();
  };
}

// 文件验证函数
export function validateFile(file: Express.Multer.File): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // 检查文件大小
  if (file.size > config.upload.maxFileSize) {
    errors.push(`File size exceeds maximum allowed size of ${config.upload.maxFileSize} bytes`);
  }

  // 检查文件类型
  if (!config.upload.allowedMimeTypes.includes(file.mimetype)) {
    errors.push(`File type ${file.mimetype} is not allowed`);
  }

  // 检查文件扩展名
  const extension = file.originalname.split('.').pop()?.toLowerCase();
  if (!extension || !config.upload.allowedExtensions.includes(extension)) {
    errors.push(`File extension .${extension} is not allowed`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// 自定义验证函数
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

export function sanitizeString(str: string): string {
  return str.trim().replace(/\s+/g, ' ');
}

export function sanitizeFileName(fileName: string): string {
  // 移除或替换非法字符
  return fileName
    .replace(/[<>:"/\\|?*\x00-\x1f]/g, '_')
    .replace(/\s+/g, '_')
    .substring(0, 255);
}
