# DocMind 生产环境配置模板
# 复制此文件为 production.env 并填入实际值

# 应用配置
NODE_ENV=production
APP_NAME=DocMind
APP_VERSION=1.0.0
APP_PORT=3000

# 数据库配置
DATABASE_URL=****************************************/database
MONGODB_URL=***********************************************
REDIS_URL=redis://host:6379

# 文件存储配置
MINIO_ENDPOINT=your-minio-endpoint
MINIO_ACCESS_KEY=your-access-key
MINIO_SECRET_KEY=your-secret-key
MINIO_BUCKET=docmind-documents
MINIO_USE_SSL=true

# 搜索引擎配置
ELASTICSEARCH_URL=https://your-elasticsearch-endpoint
ELASTICSEARCH_INDEX=docmind

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-64-characters-long-for-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# 加密配置
BCRYPT_ROUNDS=12
ENCRYPTION_KEY=your-32-character-encryption-key

# 邮件配置
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-smtp-user
SMTP_PASS=your-smtp-password
SMTP_FROM=<EMAIL>

# 短信配置
SMS_PROVIDER=aliyun
SMS_ACCESS_KEY=your-sms-access-key
SMS_SECRET_KEY=your-sms-secret-key

# AI服务配置
AI_SERVICE_URL=https://your-ai-service-endpoint
AI_SERVICE_API_KEY=your-ai-service-api-key
OPENAI_API_KEY=your-openai-api-key

# 文件上传配置
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,jpg,jpeg,png

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=/var/log/docmind/app.log

# 缓存配置
CACHE_TTL=3600
CACHE_PREFIX=docmind:

# 会话配置
SESSION_SECRET=your-session-secret-key-64-characters-long
SESSION_TIMEOUT=86400

# CORS配置
CORS_ORIGIN=https://yourdomain.com
CORS_CREDENTIALS=true

# 限流配置
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# 监控配置
PROMETHEUS_PORT=9090
HEALTH_CHECK_INTERVAL=30000

# 安全配置
HELMET_ENABLED=true
CSRF_PROTECTION=true
XSS_PROTECTION=true

# SSL配置
SSL_CERT_PATH=/etc/ssl/certs/cert.pem
SSL_KEY_PATH=/etc/ssl/private/key.pem

# 备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
