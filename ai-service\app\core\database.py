import asyncio
from typing import Optional
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
import redis.asyncio as redis
import asyncpg
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from app.config import settings
from app.core.logging import logger, log_database_operation
from app.core.exceptions import DatabaseError

# Global database connections
mongodb_client: Optional[AsyncIOMotorClient] = None
mongodb_database: Optional[AsyncIOMotorDatabase] = None
redis_client: Optional[redis.Redis] = None
postgres_engine = None
postgres_session_factory = None

async def init_databases():
    """Initialize all database connections"""
    try:
        await init_mongodb()
        await init_redis()
        await init_postgres()
        logger.info("All databases initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize databases: {e}")
        raise DatabaseError(f"Database initialization failed: {e}")

async def close_databases():
    """Close all database connections"""
    try:
        await close_mongodb()
        await close_redis()
        await close_postgres()
        logger.info("All database connections closed")
    except Exception as e:
        logger.error(f"Error closing databases: {e}")

# MongoDB
async def init_mongodb():
    """Initialize MongoDB connection"""
    global mongodb_client, mongodb_database
    
    try:
        mongodb_client = AsyncIOMotorClient(settings.mongodb_url)
        # Test connection
        await mongodb_client.admin.command('ping')
        
        # Get database name from URL
        db_name = settings.mongodb_url.split('/')[-1]
        mongodb_database = mongodb_client[db_name]
        
        logger.info("MongoDB connected successfully")
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {e}")
        raise DatabaseError(f"MongoDB connection failed: {e}")

async def close_mongodb():
    """Close MongoDB connection"""
    global mongodb_client
    if mongodb_client:
        mongodb_client.close()
        mongodb_client = None

def get_mongodb() -> AsyncIOMotorDatabase:
    """Get MongoDB database instance"""
    if mongodb_database is None:
        raise DatabaseError("MongoDB not initialized")
    return mongodb_database

# Redis
async def init_redis():
    """Initialize Redis connection"""
    global redis_client
    
    try:
        redis_client = redis.from_url(settings.redis_url)
        # Test connection
        await redis_client.ping()
        logger.info("Redis connected successfully")
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")
        raise DatabaseError(f"Redis connection failed: {e}")

async def close_redis():
    """Close Redis connection"""
    global redis_client
    if redis_client:
        await redis_client.close()
        redis_client = None

def get_redis() -> redis.Redis:
    """Get Redis client instance"""
    if redis_client is None:
        raise DatabaseError("Redis not initialized")
    return redis_client

# PostgreSQL
async def init_postgres():
    """Initialize PostgreSQL connection"""
    global postgres_engine, postgres_session_factory
    
    try:
        # Convert postgres:// to postgresql+asyncpg://
        postgres_url = settings.postgres_url.replace("postgresql://", "postgresql+asyncpg://")
        
        postgres_engine = create_async_engine(
            postgres_url,
            echo=settings.debug,
            pool_size=10,
            max_overflow=20
        )
        
        postgres_session_factory = sessionmaker(
            postgres_engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        # Test connection
        async with postgres_engine.begin() as conn:
            await conn.execute("SELECT 1")
        
        logger.info("PostgreSQL connected successfully")
    except Exception as e:
        logger.error(f"Failed to connect to PostgreSQL: {e}")
        raise DatabaseError(f"PostgreSQL connection failed: {e}")

async def close_postgres():
    """Close PostgreSQL connection"""
    global postgres_engine
    if postgres_engine:
        await postgres_engine.dispose()
        postgres_engine = None

def get_postgres_session() -> AsyncSession:
    """Get PostgreSQL session"""
    if postgres_session_factory is None:
        raise DatabaseError("PostgreSQL not initialized")
    return postgres_session_factory()

# Cache utilities
class CacheManager:
    """Redis cache manager"""
    
    def __init__(self):
        self.prefix = settings.cache_prefix
        self.default_ttl = settings.cache_ttl
    
    def _get_key(self, key: str) -> str:
        """Get prefixed cache key"""
        return f"{self.prefix}{key}"
    
    async def get(self, key: str, default=None):
        """Get value from cache"""
        try:
            redis_client = get_redis()
            value = await redis_client.get(self._get_key(key))
            if value is None:
                return default
            
            # Try to decode JSON
            import json
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value.decode('utf-8') if isinstance(value, bytes) else value
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            return default
    
    async def set(self, key: str, value, ttl: Optional[int] = None):
        """Set value in cache"""
        try:
            redis_client = get_redis()
            
            # Serialize value
            import json
            if isinstance(value, (dict, list)):
                serialized_value = json.dumps(value)
            else:
                serialized_value = str(value)
            
            ttl = ttl or self.default_ttl
            await redis_client.setex(self._get_key(key), ttl, serialized_value)
            return True
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    async def delete(self, key: str):
        """Delete key from cache"""
        try:
            redis_client = get_redis()
            await redis_client.delete(self._get_key(key))
            return True
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        try:
            redis_client = get_redis()
            return await redis_client.exists(self._get_key(key)) > 0
        except Exception as e:
            logger.error(f"Cache exists error for key {key}: {e}")
            return False
    
    async def increment(self, key: str, amount: int = 1) -> int:
        """Increment counter in cache"""
        try:
            redis_client = get_redis()
            return await redis_client.incrby(self._get_key(key), amount)
        except Exception as e:
            logger.error(f"Cache increment error for key {key}: {e}")
            raise
    
    async def expire(self, key: str, ttl: int):
        """Set expiration for key"""
        try:
            redis_client = get_redis()
            await redis_client.expire(self._get_key(key), ttl)
            return True
        except Exception as e:
            logger.error(f"Cache expire error for key {key}: {e}")
            return False
    
    async def flush_pattern(self, pattern: str):
        """Delete all keys matching pattern"""
        try:
            redis_client = get_redis()
            keys = await redis_client.keys(self._get_key(pattern))
            if keys:
                await redis_client.delete(*keys)
            return len(keys)
        except Exception as e:
            logger.error(f"Cache flush pattern error for pattern {pattern}: {e}")
            return 0

# Global cache manager instance
cache = CacheManager()

# MongoDB utilities
class MongoManager:
    """MongoDB operations manager"""
    
    def __init__(self):
        self.db = None
    
    def get_collection(self, collection_name: str):
        """Get MongoDB collection"""
        if self.db is None:
            self.db = get_mongodb()
        return self.db[collection_name]
    
    async def insert_one(self, collection_name: str, document: dict):
        """Insert single document"""
        collection = self.get_collection(collection_name)
        result = await collection.insert_one(document)
        return result.inserted_id
    
    async def insert_many(self, collection_name: str, documents: list):
        """Insert multiple documents"""
        collection = self.get_collection(collection_name)
        result = await collection.insert_many(documents)
        return result.inserted_ids
    
    async def find_one(self, collection_name: str, filter_dict: dict):
        """Find single document"""
        collection = self.get_collection(collection_name)
        return await collection.find_one(filter_dict)
    
    async def find_many(self, collection_name: str, filter_dict: dict = None, limit: int = None):
        """Find multiple documents"""
        collection = self.get_collection(collection_name)
        cursor = collection.find(filter_dict or {})
        if limit:
            cursor = cursor.limit(limit)
        return await cursor.to_list(length=limit)
    
    async def update_one(self, collection_name: str, filter_dict: dict, update_dict: dict):
        """Update single document"""
        collection = self.get_collection(collection_name)
        result = await collection.update_one(filter_dict, {"$set": update_dict})
        return result.modified_count
    
    async def update_many(self, collection_name: str, filter_dict: dict, update_dict: dict):
        """Update multiple documents"""
        collection = self.get_collection(collection_name)
        result = await collection.update_many(filter_dict, {"$set": update_dict})
        return result.modified_count
    
    async def delete_one(self, collection_name: str, filter_dict: dict):
        """Delete single document"""
        collection = self.get_collection(collection_name)
        result = await collection.delete_one(filter_dict)
        return result.deleted_count
    
    async def delete_many(self, collection_name: str, filter_dict: dict):
        """Delete multiple documents"""
        collection = self.get_collection(collection_name)
        result = await collection.delete_many(filter_dict)
        return result.deleted_count
    
    async def count_documents(self, collection_name: str, filter_dict: dict = None):
        """Count documents"""
        collection = self.get_collection(collection_name)
        return await collection.count_documents(filter_dict or {})

# Global MongoDB manager instance
mongo = MongoManager()

# Health check functions
async def check_database_health():
    """Check health of all databases"""
    health_status = {
        "mongodb": False,
        "redis": False,
        "postgres": False
    }
    
    # Check MongoDB
    try:
        db = get_mongodb()
        await db.command('ping')
        health_status["mongodb"] = True
    except Exception as e:
        logger.error(f"MongoDB health check failed: {e}")
    
    # Check Redis
    try:
        redis_client = get_redis()
        await redis_client.ping()
        health_status["redis"] = True
    except Exception as e:
        logger.error(f"Redis health check failed: {e}")
    
    # Check PostgreSQL
    try:
        async with postgres_engine.begin() as conn:
            await conn.execute("SELECT 1")
        health_status["postgres"] = True
    except Exception as e:
        logger.error(f"PostgreSQL health check failed: {e}")
    
    return health_status
