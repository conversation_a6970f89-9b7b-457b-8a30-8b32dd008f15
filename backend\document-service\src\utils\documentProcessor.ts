import fs from 'fs-extra';
import path from 'path';
import pdfParse from 'pdf-parse';
import mammoth from 'mammoth';
import sharp from 'sharp';
import { config } from '@/config';
import { logger } from '@/utils/logger';
import { 
  DocumentProcessor, 
  DocumentAnalysisResult, 
  DocumentMetadata, 
  DocumentPage,
  DocumentTable,
  DocumentImage 
} from '@/types/document';

export class DefaultDocumentProcessor implements DocumentProcessor {
  /**
   * 处理文档并提取内容
   */
  async process(filePath: string, options?: any): Promise<DocumentAnalysisResult> {
    try {
      const fileExtension = path.extname(filePath).toLowerCase();
      const documentId = options?.documentId || 'unknown';

      logger.info('Starting document processing:', { filePath, documentId, extension: fileExtension });

      let text = '';
      let pages: DocumentPage[] = [];
      let tables: DocumentTable[] = [];
      let images: DocumentImage[] = [];
      let metadata: DocumentMetadata = {};

      switch (fileExtension) {
        case '.pdf':
          ({ text, pages, tables, images, metadata } = await this.processPDF(filePath));
          break;
        case '.doc':
        case '.docx':
          ({ text, metadata } = await this.processWord(filePath));
          pages = [{ page_number: 1, text }];
          break;
        case '.txt':
          text = await this.processText(filePath);
          pages = [{ page_number: 1, text }];
          break;
        case '.jpg':
        case '.jpeg':
        case '.png':
          ({ text, metadata } = await this.processImage(filePath));
          pages = [{ page_number: 1, text }];
          break;
        default:
          throw new Error(`Unsupported file type: ${fileExtension}`);
      }

      const result: DocumentAnalysisResult = {
        document_id: documentId,
        content: {
          text,
          pages,
          tables,
          images
        },
        metadata,
        analysis: {
          language: this.detectLanguage(text),
          confidence: 0.8,
          entities: [],
          keywords: this.extractKeywords(text),
          summary: this.generateSummary(text)
        },
        created_at: new Date()
      };

      logger.info('Document processing completed:', { 
        documentId, 
        textLength: text.length, 
        pageCount: pages.length 
      });

      return result;
    } catch (error) {
      logger.error('Document processing failed:', { filePath, error });
      throw error;
    }
  }

  /**
   * 提取文本内容
   */
  async extractText(filePath: string): Promise<string> {
    const fileExtension = path.extname(filePath).toLowerCase();

    switch (fileExtension) {
      case '.pdf':
        return await this.extractPDFText(filePath);
      case '.doc':
      case '.docx':
        return await this.extractWordText(filePath);
      case '.txt':
        return await fs.readFile(filePath, 'utf-8');
      case '.jpg':
      case '.jpeg':
      case '.png':
        return await this.extractImageText(filePath);
      default:
        throw new Error(`Unsupported file type for text extraction: ${fileExtension}`);
    }
  }

  /**
   * 提取文档元数据
   */
  async extractMetadata(filePath: string): Promise<DocumentMetadata> {
    const fileExtension = path.extname(filePath).toLowerCase();
    const stats = await fs.stat(filePath);

    const baseMetadata: DocumentMetadata = {
      creation_date: stats.birthtime,
      modification_date: stats.mtime
    };

    switch (fileExtension) {
      case '.pdf':
        return { ...baseMetadata, ...(await this.extractPDFMetadata(filePath)) };
      case '.doc':
      case '.docx':
        return { ...baseMetadata, ...(await this.extractWordMetadata(filePath)) };
      default:
        return baseMetadata;
    }
  }

  /**
   * 生成缩略图
   */
  async generateThumbnail(filePath: string, outputPath: string): Promise<void> {
    const fileExtension = path.extname(filePath).toLowerCase();

    try {
      await fs.ensureDir(path.dirname(outputPath));

      switch (fileExtension) {
        case '.pdf':
          await this.generatePDFThumbnail(filePath, outputPath);
          break;
        case '.jpg':
        case '.jpeg':
        case '.png':
          await this.generateImageThumbnail(filePath, outputPath);
          break;
        default:
          // 为不支持的文件类型生成默认缩略图
          await this.generateDefaultThumbnail(outputPath);
      }

      logger.debug('Thumbnail generated:', { input: filePath, output: outputPath });
    } catch (error) {
      logger.error('Failed to generate thumbnail:', { filePath, outputPath, error });
      throw error;
    }
  }

  /**
   * 处理PDF文档
   */
  private async processPDF(filePath: string): Promise<{
    text: string;
    pages: DocumentPage[];
    tables: DocumentTable[];
    images: DocumentImage[];
    metadata: DocumentMetadata;
  }> {
    const buffer = await fs.readFile(filePath);
    const data = await pdfParse(buffer);

    const pages: DocumentPage[] = [];
    const tables: DocumentTable[] = [];
    const images: DocumentImage[] = [];

    // 简化处理：将整个文档作为一页
    pages.push({
      page_number: 1,
      text: data.text,
      confidence: 0.9
    });

    const metadata: DocumentMetadata = {
      pages: data.numpages,
      word_count: this.countWords(data.text),
      character_count: data.text.length,
      author: data.info?.Author,
      subject: data.info?.Subject,
      keywords: data.info?.Keywords ? [data.info.Keywords] : [],
      creation_date: data.info?.CreationDate,
      modification_date: data.info?.ModDate,
      application: data.info?.Creator
    };

    return {
      text: data.text,
      pages,
      tables,
      images,
      metadata
    };
  }

  /**
   * 处理Word文档
   */
  private async processWord(filePath: string): Promise<{
    text: string;
    metadata: DocumentMetadata;
  }> {
    const result = await mammoth.extractRawText({ path: filePath });
    const text = result.value;

    const metadata: DocumentMetadata = {
      word_count: this.countWords(text),
      character_count: text.length,
      format_version: path.extname(filePath) === '.docx' ? 'OOXML' : 'DOC'
    };

    return { text, metadata };
  }

  /**
   * 处理文本文档
   */
  private async processText(filePath: string): Promise<string> {
    return await fs.readFile(filePath, 'utf-8');
  }

  /**
   * 处理图片文档（OCR）
   */
  private async processImage(filePath: string): Promise<{
    text: string;
    metadata: DocumentMetadata;
  }> {
    // 这里应该集成OCR服务，暂时返回空文本
    const text = await this.extractImageText(filePath);
    
    const imageInfo = await sharp(filePath).metadata();
    const metadata: DocumentMetadata = {
      format_version: imageInfo.format,
      pages: 1
    };

    return { text, metadata };
  }

  /**
   * 提取PDF文本
   */
  private async extractPDFText(filePath: string): Promise<string> {
    const buffer = await fs.readFile(filePath);
    const data = await pdfParse(buffer);
    return data.text;
  }

  /**
   * 提取Word文本
   */
  private async extractWordText(filePath: string): Promise<string> {
    const result = await mammoth.extractRawText({ path: filePath });
    return result.value;
  }

  /**
   * 提取图片文本（OCR）
   */
  private async extractImageText(filePath: string): Promise<string> {
    // TODO: 集成OCR服务
    // 这里应该调用AI服务进行OCR识别
    logger.warn('OCR not implemented, returning empty text');
    return '';
  }

  /**
   * 提取PDF元数据
   */
  private async extractPDFMetadata(filePath: string): Promise<DocumentMetadata> {
    const buffer = await fs.readFile(filePath);
    const data = await pdfParse(buffer);

    return {
      pages: data.numpages,
      author: data.info?.Author,
      subject: data.info?.Subject,
      keywords: data.info?.Keywords ? [data.info.Keywords] : [],
      creation_date: data.info?.CreationDate,
      modification_date: data.info?.ModDate,
      application: data.info?.Creator
    };
  }

  /**
   * 提取Word元数据
   */
  private async extractWordMetadata(filePath: string): Promise<DocumentMetadata> {
    // mammoth不直接提供元数据提取，这里返回基本信息
    return {
      format_version: path.extname(filePath) === '.docx' ? 'OOXML' : 'DOC'
    };
  }

  /**
   * 生成PDF缩略图
   */
  private async generatePDFThumbnail(filePath: string, outputPath: string): Promise<void> {
    // TODO: 实现PDF缩略图生成
    // 可以使用pdf2pic或其他库
    await this.generateDefaultThumbnail(outputPath);
  }

  /**
   * 生成图片缩略图
   */
  private async generateImageThumbnail(filePath: string, outputPath: string): Promise<void> {
    await sharp(filePath)
      .resize(config.processing.thumbnailSize.width, config.processing.thumbnailSize.height, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .jpeg({ quality: 80 })
      .toFile(outputPath);
  }

  /**
   * 生成默认缩略图
   */
  private async generateDefaultThumbnail(outputPath: string): Promise<void> {
    // 生成一个简单的默认缩略图
    await sharp({
      create: {
        width: config.processing.thumbnailSize.width,
        height: config.processing.thumbnailSize.height,
        channels: 3,
        background: { r: 240, g: 240, b: 240 }
      }
    })
    .jpeg()
    .toFile(outputPath);
  }

  /**
   * 统计单词数
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * 检测语言
   */
  private detectLanguage(text: string): string {
    // 简单的语言检测，实际应用中应该使用专门的语言检测库
    const chineseRegex = /[\u4e00-\u9fff]/;
    return chineseRegex.test(text) ? 'zh' : 'en';
  }

  /**
   * 提取关键词
   */
  private extractKeywords(text: string): string[] {
    // 简单的关键词提取，实际应用中应该使用NLP库
    const words = text.toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2);

    const wordCount: Record<string, number> = {};
    words.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1;
    });

    return Object.entries(wordCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
  }

  /**
   * 生成摘要
   */
  private generateSummary(text: string): string {
    // 简单的摘要生成，取前200个字符
    return text.substring(0, 200) + (text.length > 200 ? '...' : '');
  }
}

// 导出默认处理器实例
export const documentProcessor = new DefaultDocumentProcessor();
