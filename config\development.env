# DocMind 开发环境配置

# 应用配置
NODE_ENV=development
APP_NAME=DocMind
APP_VERSION=1.0.0
APP_PORT=3000

# 数据库配置
DATABASE_URL=postgresql://docmind:docmind123@localhost:5432/docmind
MONGODB_URL=****************************************************
REDIS_URL=redis://localhost:6379

# 文件存储配置
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=docmind
MINIO_SECRET_KEY=docmind123
MINIO_BUCKET=docmind-documents
MINIO_USE_SSL=false

# 搜索引擎配置
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_INDEX=docmind

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# 加密配置
BCRYPT_ROUNDS=10
ENCRYPTION_KEY=your-32-character-encryption-key

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>

# 短信配置
SMS_PROVIDER=aliyun
SMS_ACCESS_KEY=your-sms-access-key
SMS_SECRET_KEY=your-sms-secret-key

# AI服务配置
AI_SERVICE_URL=http://localhost:8001
AI_SERVICE_API_KEY=your-ai-service-api-key
OPENAI_API_KEY=your-openai-api-key

# 文件上传配置
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,jpg,jpeg,png

# 日志配置
LOG_LEVEL=debug
LOG_FORMAT=combined
LOG_FILE=logs/app.log

# 缓存配置
CACHE_TTL=3600
CACHE_PREFIX=docmind:

# 会话配置
SESSION_SECRET=your-session-secret-key
SESSION_TIMEOUT=86400

# CORS配置
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# 限流配置
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# 监控配置
PROMETHEUS_PORT=9090
HEALTH_CHECK_INTERVAL=30000

# 开发工具配置
HOT_RELOAD=true
DEBUG_MODE=true
MOCK_EXTERNAL_SERVICES=true
