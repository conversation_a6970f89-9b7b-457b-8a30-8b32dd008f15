// Base types
export interface BaseEntity {
  id: string;
  created_at: Date;
  updated_at: Date;
}

// User types (from user service)
export interface User {
  id: string;
  email: string;
  name: string;
  avatar_url?: string;
  organization_id: string;
  role: string;
}

// Comment types
export interface Comment extends BaseEntity {
  document_id: string;
  parent_id?: string;
  author_id: string;
  content: string;
  mentions: string[];
  attachments: CommentAttachment[];
  reactions: CommentReaction[];
  status: CommentStatus;
  resolved_at?: Date;
  resolved_by?: string;
  position?: CommentPosition;
}

export interface CommentAttachment {
  id: string;
  filename: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  uploaded_at: Date;
}

export interface CommentReaction {
  user_id: string;
  emoji: string;
  created_at: Date;
}

export interface CommentPosition {
  page: number;
  x: number;
  y: number;
  width?: number;
  height?: number;
  text_selection?: {
    start: number;
    end: number;
    text: string;
  };
}

export enum CommentStatus {
  ACTIVE = 'active',
  RESOLVED = 'resolved',
  DELETED = 'deleted',
}

// Approval workflow types
export interface ApprovalWorkflow extends BaseEntity {
  document_id: string;
  name: string;
  description?: string;
  steps: ApprovalStep[];
  status: WorkflowStatus;
  created_by: string;
  current_step: number;
  completed_at?: Date;
  metadata: Record<string, any>;
}

export interface ApprovalStep {
  id: string;
  step_number: number;
  name: string;
  description?: string;
  approvers: string[];
  required_approvals: number;
  auto_approve: boolean;
  timeout_hours?: number;
  status: StepStatus;
  started_at?: Date;
  completed_at?: Date;
  approvals: Approval[];
}

export interface Approval {
  id: string;
  step_id: string;
  approver_id: string;
  status: ApprovalStatus;
  comment?: string;
  approved_at?: Date;
  metadata: Record<string, any>;
}

export enum WorkflowStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
}

export enum StepStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  SKIPPED = 'skipped',
  EXPIRED = 'expired',
}

export enum ApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  DELEGATED = 'delegated',
}

// Notification types
export interface Notification extends BaseEntity {
  recipient_id: string;
  type: NotificationType;
  title: string;
  message: string;
  data: Record<string, any>;
  read: boolean;
  read_at?: Date;
  channels: NotificationChannel[];
  priority: NotificationPriority;
}

export enum NotificationType {
  COMMENT_MENTION = 'comment_mention',
  COMMENT_REPLY = 'comment_reply',
  APPROVAL_REQUEST = 'approval_request',
  APPROVAL_APPROVED = 'approval_approved',
  APPROVAL_REJECTED = 'approval_rejected',
  WORKFLOW_COMPLETED = 'workflow_completed',
  DOCUMENT_SHARED = 'document_shared',
  SYSTEM_ANNOUNCEMENT = 'system_announcement',
}

export enum NotificationChannel {
  EMAIL = 'email',
  WEBSOCKET = 'websocket',
  PUSH = 'push',
}

export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
}

// Permission types
export interface Permission extends BaseEntity {
  resource_type: ResourceType;
  resource_id: string;
  user_id?: string;
  role?: string;
  permissions: PermissionAction[];
  granted_by: string;
  expires_at?: Date;
}

export enum ResourceType {
  DOCUMENT = 'document',
  COMMENT = 'comment',
  WORKFLOW = 'workflow',
  ORGANIZATION = 'organization',
}

export enum PermissionAction {
  READ = 'read',
  WRITE = 'write',
  DELETE = 'delete',
  COMMENT = 'comment',
  APPROVE = 'approve',
  SHARE = 'share',
  ADMIN = 'admin',
}

// Activity log types
export interface ActivityLog extends BaseEntity {
  user_id: string;
  action: ActivityAction;
  resource_type: ResourceType;
  resource_id: string;
  details: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
}

export enum ActivityAction {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  VIEW = 'view',
  COMMENT = 'comment',
  APPROVE = 'approve',
  REJECT = 'reject',
  SHARE = 'share',
  DOWNLOAD = 'download',
}

// API request/response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

// Request types
export interface CreateCommentRequest {
  document_id: string;
  parent_id?: string;
  content: string;
  mentions?: string[];
  position?: CommentPosition;
}

export interface UpdateCommentRequest {
  content?: string;
  mentions?: string[];
  status?: CommentStatus;
}

export interface CreateWorkflowRequest {
  document_id: string;
  name: string;
  description?: string;
  steps: CreateStepRequest[];
}

export interface CreateStepRequest {
  name: string;
  description?: string;
  approvers: string[];
  required_approvals: number;
  auto_approve?: boolean;
  timeout_hours?: number;
}

export interface ApprovalRequest {
  status: ApprovalStatus;
  comment?: string;
}

export interface CreateNotificationRequest {
  recipient_id: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: Record<string, any>;
  channels?: NotificationChannel[];
  priority?: NotificationPriority;
}

export interface ShareDocumentRequest {
  document_id: string;
  user_ids?: string[];
  emails?: string[];
  permissions: PermissionAction[];
  message?: string;
  expires_at?: Date;
}

// Search and filter types
export interface CommentSearchParams extends PaginationParams {
  document_id?: string;
  author_id?: string;
  status?: CommentStatus;
  resolved?: boolean;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface WorkflowSearchParams extends PaginationParams {
  document_id?: string;
  status?: WorkflowStatus;
  created_by?: string;
  date_from?: string;
  date_to?: string;
}

export interface NotificationSearchParams extends PaginationParams {
  recipient_id?: string;
  type?: NotificationType;
  read?: boolean;
  priority?: NotificationPriority;
  date_from?: string;
  date_to?: string;
}

// WebSocket types
export interface WebSocketMessage {
  type: WebSocketMessageType;
  data: any;
  timestamp: Date;
}

export enum WebSocketMessageType {
  COMMENT_CREATED = 'comment_created',
  COMMENT_UPDATED = 'comment_updated',
  COMMENT_DELETED = 'comment_deleted',
  APPROVAL_REQUEST = 'approval_request',
  APPROVAL_RESPONSE = 'approval_response',
  NOTIFICATION = 'notification',
  USER_JOINED = 'user_joined',
  USER_LEFT = 'user_left',
  TYPING = 'typing',
}

// Error types
export interface AppError extends Error {
  statusCode: number;
  code: string;
  details?: any;
}

// Configuration types
export interface EmailConfig {
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject: string;
  template: string;
  data: Record<string, any>;
}

export interface WebSocketConfig {
  room: string;
  event: string;
  data: any;
  excludeUser?: string;
}
