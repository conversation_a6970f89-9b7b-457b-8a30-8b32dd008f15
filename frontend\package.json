{"name": "doc<PERSON>-frontend", "private": true, "version": "1.0.0", "type": "module", "description": "DocMind Frontend - 智能合同管理系统前端", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "axios": "^1.6.2", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-hot-toast": "^2.4.1", "react-dropzone": "^14.2.3", "react-pdf": "^7.5.1", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-table": "^7.8.0", "@types/react-table": "^7.7.18", "react-virtual": "^2.10.4", "react-window": "^1.8.8", "@types/react-window": "^1.8.8", "react-beautiful-dnd": "^13.1.1", "@types/react-beautiful-dnd": "^13.1.8", "react-select": "^5.8.0", "react-datepicker": "^4.24.0", "@types/react-datepicker": "^4.19.4", "react-loading-skeleton": "^3.3.1", "react-intersection-observer": "^9.5.3", "react-use": "^17.4.0", "lodash": "^4.17.21", "@types/lodash": "^4.14.202", "dayjs": "^1.11.10", "uuid": "^9.0.1", "@types/uuid": "^9.0.7"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "typescript": "^5.2.2", "vite": "^5.0.0", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "vitest": "^0.34.6", "@vitest/ui": "^0.34.6", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jsdom": "^23.0.1", "msw": "^2.0.8"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}