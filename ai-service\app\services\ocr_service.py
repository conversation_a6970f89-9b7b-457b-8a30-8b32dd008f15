import asyncio
import time
from typing import List, Optional, Tuple
import cv2
import numpy as np
from PIL import Image
import pytesseract
from pdf2image import convert_from_path
import os

from app.config import settings, get_model_config
from app.core.models import get_model
from app.core.logging import logger, log_execution_time
from app.core.exceptions import OCRError, FileNotFoundError, UnsupportedFileTypeError
from app.models.schemas import (
    OCRRequest, OCRResult, OCRPage, OCRLine, OCRWord, OCRBoundingBox
)

class OCRService:
    """OCR Service for text extraction from images and documents"""
    
    def __init__(self):
        self.tesseract_config = get_model_config("ocr", "tesseract")
    
    async def process_document(self, request: OCRRequest) -> OCRResult:
        """Process document for OCR"""
        start_time = time.time()
        
        try:
            # Validate file exists
            if not os.path.exists(request.file_path):
                raise FileNotFoundError(request.file_path)
            
            # Determine file type and process accordingly
            file_extension = os.path.splitext(request.file_path)[1].lower()
            
            if file_extension == '.pdf':
                pages = await self._process_pdf(request)
            elif file_extension in ['.jpg', '.jpeg', '.png', '.tiff', '.bmp']:
                pages = await self._process_image(request)
            else:
                raise UnsupportedFileTypeError(file_extension)
            
            # Calculate overall statistics
            full_text = "\n\n".join([page.text for page in pages])
            total_confidence = sum([page.confidence for page in pages]) / len(pages) if pages else 0
            processing_time = time.time() - start_time
            
            result = OCRResult(
                document_id=request.document_id,
                total_pages=len(pages),
                pages=pages,
                full_text=full_text,
                average_confidence=total_confidence,
                processing_time=processing_time,
                metadata={
                    "languages": request.languages,
                    "confidence_threshold": request.confidence_threshold,
                    "preprocessed": request.preprocess
                }
            )
            
            logger.info(f"OCR processing completed for document {request.document_id}", 
                       extra={
                           "document_id": request.document_id,
                           "pages": len(pages),
                           "confidence": total_confidence,
                           "processing_time": processing_time
                       })
            
            return result
            
        except Exception as e:
            logger.error(f"OCR processing failed for document {request.document_id}: {e}")
            raise OCRError(str(e), metadata={"document_id": request.document_id})
    
    async def _process_pdf(self, request: OCRRequest) -> List[OCRPage]:
        """Process PDF document"""
        pages = []
        
        try:
            # Convert PDF to images
            loop = asyncio.get_event_loop()
            images = await loop.run_in_executor(
                None, 
                convert_from_path, 
                request.file_path,
                dpi=300,
                fmt='RGB'
            )
            
            # Process each page
            for page_num, image in enumerate(images, 1):
                page_result = await self._process_single_image(
                    image, page_num, request
                )
                pages.append(page_result)
            
            return pages
            
        except Exception as e:
            raise OCRError(f"PDF processing failed: {e}")
    
    async def _process_image(self, request: OCRRequest) -> List[OCRPage]:
        """Process single image file"""
        try:
            image = Image.open(request.file_path)
            page_result = await self._process_single_image(image, 1, request)
            return [page_result]
            
        except Exception as e:
            raise OCRError(f"Image processing failed: {e}")
    
    async def _process_single_image(
        self, 
        image: Image.Image, 
        page_number: int, 
        request: OCRRequest
    ) -> OCRPage:
        """Process a single image for OCR"""
        try:
            # Preprocess image if requested
            if request.preprocess:
                image = await self._preprocess_image(image)
            
            # Get Tesseract model
            tesseract = get_model("tesseract")
            
            # Configure Tesseract
            config = self.tesseract_config["config"]
            languages = "+".join(request.languages)
            
            # Run OCR in thread pool
            loop = asyncio.get_event_loop()
            
            # Get detailed OCR data
            ocr_data = await loop.run_in_executor(
                None,
                lambda: pytesseract.image_to_data(
                    image,
                    lang=languages,
                    config=config,
                    output_type=pytesseract.Output.DICT
                )
            )
            
            # Get full text
            full_text = await loop.run_in_executor(
                None,
                lambda: pytesseract.image_to_string(
                    image,
                    lang=languages,
                    config=config
                )
            )
            
            # Parse OCR data into structured format
            lines = self._parse_ocr_data(ocr_data, request.confidence_threshold)
            
            # Calculate page confidence
            page_confidence = self._calculate_page_confidence(ocr_data, request.confidence_threshold)
            
            # Get page bounding box
            page_bbox = OCRBoundingBox(
                x=0, y=0, 
                width=image.width, 
                height=image.height
            )
            
            return OCRPage(
                page_number=page_number,
                text=full_text.strip(),
                confidence=page_confidence,
                lines=lines,
                bbox=page_bbox,
                language=self._detect_language(full_text, request.languages)
            )
            
        except Exception as e:
            raise OCRError(f"Single image processing failed: {e}")
    
    async def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """Preprocess image to improve OCR accuracy"""
        try:
            # Convert PIL image to OpenCV format
            opencv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # Convert to grayscale
            gray = cv2.cvtColor(opencv_image, cv2.COLOR_BGR2GRAY)
            
            # Apply denoising
            denoised = cv2.fastNlMeansDenoising(gray)
            
            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
            
            # Apply morphological operations to clean up
            kernel = np.ones((1, 1), np.uint8)
            cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            # Convert back to PIL Image
            processed_image = Image.fromarray(cleaned)
            
            return processed_image
            
        except Exception as e:
            logger.warning(f"Image preprocessing failed: {e}, using original image")
            return image
    
    def _parse_ocr_data(self, ocr_data: dict, confidence_threshold: float) -> List[OCRLine]:
        """Parse Tesseract OCR data into structured format"""
        lines = []
        current_line = None
        current_words = []
        
        for i in range(len(ocr_data['text'])):
            confidence = float(ocr_data['conf'][i])
            text = ocr_data['text'][i].strip()
            
            # Skip low confidence or empty text
            if confidence < confidence_threshold * 100 or not text:
                continue
            
            level = ocr_data['level'][i]
            
            # Level 5 is word level
            if level == 5:
                word = OCRWord(
                    text=text,
                    confidence=confidence / 100.0,
                    bbox=OCRBoundingBox(
                        x=float(ocr_data['left'][i]),
                        y=float(ocr_data['top'][i]),
                        width=float(ocr_data['width'][i]),
                        height=float(ocr_data['height'][i])
                    )
                )
                current_words.append(word)
            
            # Level 4 is line level
            elif level == 4 and current_words:
                # Create line from accumulated words
                line_text = " ".join([word.text for word in current_words])
                line_confidence = sum([word.confidence for word in current_words]) / len(current_words)
                
                # Calculate line bounding box
                min_x = min([word.bbox.x for word in current_words])
                min_y = min([word.bbox.y for word in current_words])
                max_x = max([word.bbox.x + word.bbox.width for word in current_words])
                max_y = max([word.bbox.y + word.bbox.height for word in current_words])
                
                line = OCRLine(
                    text=line_text,
                    confidence=line_confidence,
                    words=current_words,
                    bbox=OCRBoundingBox(
                        x=min_x, y=min_y,
                        width=max_x - min_x,
                        height=max_y - min_y
                    )
                )
                lines.append(line)
                current_words = []
        
        # Handle remaining words
        if current_words:
            line_text = " ".join([word.text for word in current_words])
            line_confidence = sum([word.confidence for word in current_words]) / len(current_words)
            
            min_x = min([word.bbox.x for word in current_words])
            min_y = min([word.bbox.y for word in current_words])
            max_x = max([word.bbox.x + word.bbox.width for word in current_words])
            max_y = max([word.bbox.y + word.bbox.height for word in current_words])
            
            line = OCRLine(
                text=line_text,
                confidence=line_confidence,
                words=current_words,
                bbox=OCRBoundingBox(
                    x=min_x, y=min_y,
                    width=max_x - min_x,
                    height=max_y - min_y
                )
            )
            lines.append(line)
        
        return lines
    
    def _calculate_page_confidence(self, ocr_data: dict, confidence_threshold: float) -> float:
        """Calculate overall page confidence"""
        confidences = [
            float(conf) for conf in ocr_data['conf'] 
            if float(conf) >= confidence_threshold * 100
        ]
        
        if not confidences:
            return 0.0
        
        return sum(confidences) / len(confidences) / 100.0
    
    def _detect_language(self, text: str, available_languages: List[str]) -> Optional[str]:
        """Simple language detection"""
        # Simple heuristic: check for Chinese characters
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        total_chars = len([char for char in text if char.isalpha()])
        
        if total_chars == 0:
            return None
        
        chinese_ratio = chinese_chars / total_chars
        
        if chinese_ratio > 0.3 and 'chi_sim' in available_languages:
            return 'chi_sim'
        elif 'eng' in available_languages:
            return 'eng'
        else:
            return available_languages[0] if available_languages else None

# Global OCR service instance
ocr_service = OCRService()
