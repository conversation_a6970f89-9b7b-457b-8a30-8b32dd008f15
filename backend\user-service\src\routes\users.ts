import { Router } from 'express';
import { UserController } from '@/controllers/UserController';
import { 
  authenticateToken, 
  validateUser, 
  requireRole, 
  requireResourceOwner,
  auditLog 
} from '@/middleware/auth';
import {
  validateBody,
  validateQuery,
  validateParams,
  updateUserSchema,
  paginationSchema,
  idParamSchema
} from '@/utils/validation';
import { OrganizationRole } from '@/types/user';

const router = Router();

// 所有用户路由都需要认证
router.use(authenticateToken);
router.use(validateUser);

/**
 * @swagger
 * /api/users:
 *   get:
 *     summary: 获取用户列表
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [created_at, updated_at, name, email]
 *           default: created_at
 *       - in: query
 *         name: order
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive, suspended]
 *     responses:
 *       200:
 *         description: 用户列表获取成功
 *       401:
 *         description: 未认证
 *       403:
 *         description: 权限不足
 */
router.get('/',
  requireRole(OrganizationRole.ADMIN, OrganizationRole.OWNER),
  validateQuery(paginationSchema),
  UserController.getUsers
);

/**
 * @swagger
 * /api/users/search:
 *   get:
 *     summary: 搜索用户
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *     responses:
 *       200:
 *         description: 搜索结果获取成功
 *       401:
 *         description: 未认证
 */
router.get('/search',
  validateQuery(paginationSchema),
  UserController.searchUsers
);

/**
 * @swagger
 * /api/users/stats:
 *   get:
 *     summary: 获取用户统计信息
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 统计信息获取成功
 *       401:
 *         description: 未认证
 *       403:
 *         description: 权限不足
 */
router.get('/stats',
  requireRole(OrganizationRole.ADMIN, OrganizationRole.OWNER),
  UserController.getUserStats
);

/**
 * @swagger
 * /api/users/export:
 *   get:
 *     summary: 导出用户数据
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 导出成功
 *       401:
 *         description: 未认证
 *       403:
 *         description: 权限不足
 */
router.get('/export',
  requireRole(OrganizationRole.ADMIN, OrganizationRole.OWNER),
  auditLog('user.export', 'user'),
  UserController.exportUsers
);

/**
 * @swagger
 * /api/users/{id}:
 *   get:
 *     summary: 根据ID获取用户
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: 用户信息获取成功
 *       401:
 *         description: 未认证
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 用户不存在
 */
router.get('/:id',
  validateParams(idParamSchema),
  requireResourceOwner('id'),
  UserController.getUserById
);

/**
 * @swagger
 * /api/users/{id}:
 *   put:
 *     summary: 更新用户信息
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               phone:
 *                 type: string
 *               avatar_url:
 *                 type: string
 *                 format: uri
 *     responses:
 *       200:
 *         description: 用户信息更新成功
 *       401:
 *         description: 未认证
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 用户不存在
 */
router.put('/:id',
  validateParams(idParamSchema),
  validateBody(updateUserSchema),
  requireResourceOwner('id'),
  auditLog('user.update', 'user'),
  UserController.updateUser
);

/**
 * @swagger
 * /api/users/{id}:
 *   delete:
 *     summary: 删除用户
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: 用户删除成功
 *       401:
 *         description: 未认证
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 用户不存在
 */
router.delete('/:id',
  validateParams(idParamSchema),
  requireRole(OrganizationRole.ADMIN, OrganizationRole.OWNER),
  auditLog('user.delete', 'user'),
  UserController.deleteUser
);

/**
 * @swagger
 * /api/users/bulk/delete:
 *   post:
 *     summary: 批量删除用户
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userIds
 *             properties:
 *               userIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *     responses:
 *       200:
 *         description: 批量删除成功
 *       401:
 *         description: 未认证
 *       403:
 *         description: 权限不足
 */
router.post('/bulk/delete',
  requireRole(OrganizationRole.ADMIN, OrganizationRole.OWNER),
  auditLog('user.bulk_delete', 'user'),
  UserController.bulkDeleteUsers
);

/**
 * @swagger
 * /api/users/{id}/reset-password:
 *   post:
 *     summary: 重置用户密码（管理员功能）
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: 密码重置成功
 *       401:
 *         description: 未认证
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 用户不存在
 */
router.post('/:id/reset-password',
  validateParams(idParamSchema),
  requireRole(OrganizationRole.ADMIN, OrganizationRole.OWNER),
  auditLog('password.admin_reset', 'user'),
  UserController.resetUserPassword
);

/**
 * @swagger
 * /api/users/{id}/toggle-status:
 *   post:
 *     summary: 激活/停用用户
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [active, inactive, suspended]
 *     responses:
 *       200:
 *         description: 状态更新成功
 *       401:
 *         description: 未认证
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 用户不存在
 */
router.post('/:id/toggle-status',
  validateParams(idParamSchema),
  requireRole(OrganizationRole.ADMIN, OrganizationRole.OWNER),
  auditLog('user.status_change', 'user'),
  UserController.toggleUserStatus
);

export { router as userRoutes };
