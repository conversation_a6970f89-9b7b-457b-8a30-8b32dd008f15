import asyncio
import time
import re
from typing import List, Dict, Any, <PERSON><PERSON>, <PERSON><PERSON>
from collections import defaultdict

from app.config import settings, get_risk_patterns, MODEL_CONFIGS
from app.core.models import get_model
from app.core.logging import logger, log_execution_time
from app.core.exceptions import RiskAnalysisError
from app.models.schemas import (
    RiskAnalysisRequest, RiskAnalysisResult, RiskFactor, ComplianceCheck,
    RiskLevel, DocumentType
)

class RiskAnalysisService:
    """Risk Analysis Service for contract and document risk assessment"""
    
    def __init__(self):
        self.risk_patterns = get_risk_patterns()
        self.risk_config = MODEL_CONFIGS["risk_analysis"]
        self.compliance_rules = self._load_compliance_rules()
    
    async def analyze_risk(self, request: RiskAnalysisRequest) -> RiskAnalysisResult:
        """Analyze document for risks"""
        start_time = time.time()
        
        try:
            # Initialize analysis components
            risk_factors = []
            compliance_checks = []
            recommendations = []
            
            # Perform different types of risk analysis
            risk_factors.extend(await self._analyze_content_risks(request.text, request.document_type))
            risk_factors.extend(await self._analyze_legal_risks(request.text))
            risk_factors.extend(await self._analyze_financial_risks(request.text))
            risk_factors.extend(await self._analyze_operational_risks(request.text))
            
            # Perform compliance checks
            compliance_checks = await self._perform_compliance_checks(request.text, request.document_type)
            
            # Calculate overall risk level and score
            overall_risk_level, overall_risk_score = self._calculate_overall_risk(risk_factors)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(risk_factors, compliance_checks)
            
            processing_time = time.time() - start_time
            
            result = RiskAnalysisResult(
                document_id=request.document_id,
                overall_risk_level=overall_risk_level,
                overall_risk_score=overall_risk_score,
                risk_factors=risk_factors,
                compliance_checks=compliance_checks,
                recommendations=recommendations,
                processing_time=processing_time,
                metadata={
                    "document_type": request.document_type,
                    "analysis_depth": request.analysis_depth,
                    "text_length": len(request.text),
                    "risk_factors_count": len(risk_factors),
                    "compliance_checks_count": len(compliance_checks)
                }
            )
            
            logger.info(f"Risk analysis completed for document {request.document_id}",
                       extra={
                           "document_id": request.document_id,
                           "overall_risk_level": overall_risk_level,
                           "overall_risk_score": overall_risk_score,
                           "risk_factors_count": len(risk_factors),
                           "processing_time": processing_time
                       })
            
            return result
            
        except Exception as e:
            logger.error(f"Risk analysis failed for document {request.document_id}: {e}")
            raise RiskAnalysisError(str(e), metadata={"document_id": request.document_id})
    
    async def _analyze_content_risks(self, text: str, document_type: DocumentType) -> List[RiskFactor]:
        """Analyze content-based risks"""
        risk_factors = []
        
        # High risk patterns
        for pattern in self.risk_patterns["high_risk"]:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                risk_factors.append(RiskFactor(
                    category="content_risk",
                    description=f"High risk clause detected: {pattern}",
                    risk_level=RiskLevel.HIGH,
                    confidence=0.9,
                    location=f"Position {match.start()}-{match.end()}",
                    recommendation=f"Review and consider modifying: {match.group()}"
                ))
        
        # Medium risk patterns
        for pattern in self.risk_patterns["medium_risk"]:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                risk_factors.append(RiskFactor(
                    category="content_risk",
                    description=f"Medium risk clause detected: {pattern}",
                    risk_level=RiskLevel.MEDIUM,
                    confidence=0.7,
                    location=f"Position {match.start()}-{match.end()}",
                    recommendation=f"Consider reviewing: {match.group()}"
                ))
        
        return risk_factors
    
    async def _analyze_legal_risks(self, text: str) -> List[RiskFactor]:
        """Analyze legal risks"""
        risk_factors = []
        
        # Check for liability clauses
        liability_patterns = [
            r"unlimited\s+liability|无限责任",
            r"personal\s+guarantee|个人担保",
            r"joint\s+and\s+several\s+liability|连带责任",
            r"indemnification|赔偿条款"
        ]
        
        for pattern in liability_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                risk_level = RiskLevel.HIGH if "unlimited" in match.group().lower() else RiskLevel.MEDIUM
                risk_factors.append(RiskFactor(
                    category="legal_risk",
                    description=f"Liability clause detected: {match.group()}",
                    risk_level=risk_level,
                    confidence=0.8,
                    location=f"Position {match.start()}-{match.end()}",
                    recommendation="Review liability terms and consider limitations"
                ))
        
        # Check for termination clauses
        termination_patterns = [
            r"terminate\s+at\s+will|随意终止",
            r"immediate\s+termination|立即终止",
            r"without\s+cause|无故终止"
        ]
        
        for pattern in termination_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                risk_factors.append(RiskFactor(
                    category="legal_risk",
                    description=f"Termination clause detected: {match.group()}",
                    risk_level=RiskLevel.MEDIUM,
                    confidence=0.7,
                    location=f"Position {match.start()}-{match.end()}",
                    recommendation="Ensure termination terms are balanced and fair"
                ))
        
        return risk_factors
    
    async def _analyze_financial_risks(self, text: str) -> List[RiskFactor]:
        """Analyze financial risks"""
        risk_factors = []
        
        # Check for payment terms
        payment_patterns = [
            r"payment\s+due\s+immediately|立即付款",
            r"advance\s+payment|预付款",
            r"penalty\s+for\s+late\s+payment|逾期付款罚金",
            r"interest\s+rate.*?(\d+(?:\.\d+)?%)|利率.*?(\d+(?:\.\d+)?%)"
        ]
        
        for pattern in payment_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                risk_level = RiskLevel.MEDIUM
                if "immediately" in match.group().lower() or "advance" in match.group().lower():
                    risk_level = RiskLevel.HIGH
                
                risk_factors.append(RiskFactor(
                    category="financial_risk",
                    description=f"Payment term detected: {match.group()}",
                    risk_level=risk_level,
                    confidence=0.8,
                    location=f"Position {match.start()}-{match.end()}",
                    recommendation="Review payment terms for fairness and feasibility"
                ))
        
        # Check for currency and exchange rate risks
        currency_patterns = [
            r"foreign\s+currency|外币",
            r"exchange\s+rate|汇率",
            r"currency\s+fluctuation|汇率波动"
        ]
        
        for pattern in currency_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                risk_factors.append(RiskFactor(
                    category="financial_risk",
                    description=f"Currency risk detected: {match.group()}",
                    risk_level=RiskLevel.MEDIUM,
                    confidence=0.6,
                    location=f"Position {match.start()}-{match.end()}",
                    recommendation="Consider currency hedging or fixed exchange rates"
                ))
        
        return risk_factors
    
    async def _analyze_operational_risks(self, text: str) -> List[RiskFactor]:
        """Analyze operational risks"""
        risk_factors = []
        
        # Check for performance obligations
        performance_patterns = [
            r"strict\s+performance|严格履行",
            r"time\s+is\s+of\s+the\s+essence|时间至关重要",
            r"liquidated\s+damages|违约金",
            r"service\s+level\s+agreement|服务水平协议"
        ]
        
        for pattern in performance_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                risk_level = RiskLevel.HIGH if "strict" in match.group().lower() else RiskLevel.MEDIUM
                risk_factors.append(RiskFactor(
                    category="operational_risk",
                    description=f"Performance obligation detected: {match.group()}",
                    risk_level=risk_level,
                    confidence=0.7,
                    location=f"Position {match.start()}-{match.end()}",
                    recommendation="Ensure performance requirements are achievable"
                ))
        
        # Check for confidentiality and IP risks
        ip_patterns = [
            r"intellectual\s+property\s+assignment|知识产权转让",
            r"work\s+for\s+hire|雇佣作品",
            r"confidential\s+information|保密信息",
            r"non-disclosure|保密协议"
        ]
        
        for pattern in ip_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                risk_factors.append(RiskFactor(
                    category="operational_risk",
                    description=f"IP/Confidentiality clause detected: {match.group()}",
                    risk_level=RiskLevel.MEDIUM,
                    confidence=0.8,
                    location=f"Position {match.start()}-{match.end()}",
                    recommendation="Review IP and confidentiality terms carefully"
                ))
        
        return risk_factors
    
    async def _perform_compliance_checks(self, text: str, document_type: DocumentType) -> List[ComplianceCheck]:
        """Perform compliance checks"""
        compliance_checks = []
        
        for rule_name, rule_config in self.compliance_rules.items():
            if document_type.value in rule_config.get("applicable_types", []):
                check_result = await self._check_compliance_rule(text, rule_config)
                compliance_checks.append(ComplianceCheck(
                    regulation=rule_name,
                    status=check_result["status"],
                    confidence=check_result["confidence"],
                    details=check_result["details"]
                ))
        
        return compliance_checks
    
    async def _check_compliance_rule(self, text: str, rule_config: Dict[str, Any]) -> Dict[str, Any]:
        """Check a specific compliance rule"""
        required_patterns = rule_config.get("required_patterns", [])
        prohibited_patterns = rule_config.get("prohibited_patterns", [])
        
        # Check required patterns
        required_found = 0
        for pattern in required_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                required_found += 1
        
        # Check prohibited patterns
        prohibited_found = 0
        for pattern in prohibited_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                prohibited_found += 1
        
        # Determine compliance status
        if prohibited_found > 0:
            status = "non_compliant"
            confidence = 0.8
            details = f"Found {prohibited_found} prohibited patterns"
        elif required_found == len(required_patterns):
            status = "compliant"
            confidence = 0.9
            details = "All required patterns found"
        elif required_found > 0:
            status = "unclear"
            confidence = 0.6
            details = f"Found {required_found}/{len(required_patterns)} required patterns"
        else:
            status = "non_compliant"
            confidence = 0.7
            details = "No required patterns found"
        
        return {
            "status": status,
            "confidence": confidence,
            "details": details
        }
    
    def _calculate_overall_risk(self, risk_factors: List[RiskFactor]) -> Tuple[RiskLevel, float]:
        """Calculate overall risk level and score"""
        if not risk_factors:
            return RiskLevel.LOW, 0.0
        
        # Weight risks by level
        risk_weights = {
            RiskLevel.LOW: 0.2,
            RiskLevel.MEDIUM: 0.5,
            RiskLevel.HIGH: 0.8,
            RiskLevel.CRITICAL: 1.0
        }
        
        total_score = 0.0
        total_weight = 0.0
        
        for factor in risk_factors:
            weight = risk_weights[factor.risk_level] * factor.confidence
            total_score += weight
            total_weight += factor.confidence
        
        if total_weight == 0:
            return RiskLevel.LOW, 0.0
        
        average_score = total_score / total_weight
        
        # Determine overall risk level
        thresholds = self.risk_config["thresholds"]
        if average_score >= thresholds["high"]:
            return RiskLevel.HIGH, average_score
        elif average_score >= thresholds["medium"]:
            return RiskLevel.MEDIUM, average_score
        else:
            return RiskLevel.LOW, average_score
    
    def _generate_recommendations(self, risk_factors: List[RiskFactor], compliance_checks: List[ComplianceCheck]) -> List[str]:
        """Generate recommendations based on analysis"""
        recommendations = []
        
        # Risk-based recommendations
        high_risk_count = sum(1 for factor in risk_factors if factor.risk_level == RiskLevel.HIGH)
        if high_risk_count > 0:
            recommendations.append(f"Address {high_risk_count} high-risk factors before proceeding")
        
        medium_risk_count = sum(1 for factor in risk_factors if factor.risk_level == RiskLevel.MEDIUM)
        if medium_risk_count > 2:
            recommendations.append(f"Review {medium_risk_count} medium-risk factors for potential mitigation")
        
        # Compliance-based recommendations
        non_compliant_count = sum(1 for check in compliance_checks if check.status == "non_compliant")
        if non_compliant_count > 0:
            recommendations.append(f"Address {non_compliant_count} compliance issues")
        
        # Category-specific recommendations
        risk_categories = defaultdict(int)
        for factor in risk_factors:
            risk_categories[factor.category] += 1
        
        for category, count in risk_categories.items():
            if count > 2:
                recommendations.append(f"Multiple {category.replace('_', ' ')} issues detected - consider comprehensive review")
        
        # General recommendations
        if len(risk_factors) > 5:
            recommendations.append("Consider legal review due to multiple risk factors")
        
        if not recommendations:
            recommendations.append("Document appears to have acceptable risk levels")
        
        return recommendations
    
    def _load_compliance_rules(self) -> Dict[str, Dict[str, Any]]:
        """Load compliance rules configuration"""
        return {
            "data_protection": {
                "applicable_types": ["contract", "other"],
                "required_patterns": [
                    r"data\s+protection|数据保护",
                    r"privacy\s+policy|隐私政策"
                ],
                "prohibited_patterns": [
                    r"unlimited\s+data\s+use|无限制数据使用"
                ]
            },
            "anti_corruption": {
                "applicable_types": ["contract"],
                "required_patterns": [
                    r"anti-corruption|反腐败",
                    r"bribery\s+prohibition|禁止贿赂"
                ],
                "prohibited_patterns": [
                    r"facilitation\s+payment|便利费"
                ]
            },
            "labor_law": {
                "applicable_types": ["contract"],
                "required_patterns": [
                    r"working\s+hours|工作时间",
                    r"overtime\s+compensation|加班补偿"
                ],
                "prohibited_patterns": [
                    r"unpaid\s+overtime|无偿加班"
                ]
            }
        }

# Global risk analysis service instance
risk_analysis_service = RiskAnalysisService()
