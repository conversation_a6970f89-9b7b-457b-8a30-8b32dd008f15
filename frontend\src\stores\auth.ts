import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, LoginForm, RegisterForm } from '@/types';
import AuthService from '@/services/auth';

interface AuthState {
  // 状态
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // 操作
  login: (credentials: LoginForm) => Promise<void>;
  register: (userData: RegisterForm) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  getCurrentUser: () => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  changePassword: (data: { current_password: string; new_password: string }) => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  checkAuth: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // 登录
      login: async (credentials: LoginForm) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await AuthService.login(credentials);
          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: error instanceof Error ? error.message : '登录失败',
          });
          throw error;
        }
      },

      // 注册
      register: async (userData: RegisterForm) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await AuthService.register(userData);
          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: error instanceof Error ? error.message : '注册失败',
          });
          throw error;
        }
      },

      // 登出
      logout: async () => {
        set({ isLoading: true });
        
        try {
          await AuthService.logout();
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      },

      // 刷新token
      refreshToken: async () => {
        try {
          await AuthService.refreshToken();
          // Token刷新成功，不需要更新状态
        } catch (error) {
          // Token刷新失败，清除认证状态
          set({
            user: null,
            isAuthenticated: false,
            error: '登录已过期，请重新登录',
          });
          throw error;
        }
      },

      // 获取当前用户信息
      getCurrentUser: async () => {
        set({ isLoading: true, error: null });
        
        try {
          const user = await AuthService.getCurrentUser();
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: error instanceof Error ? error.message : '获取用户信息失败',
          });
          throw error;
        }
      },

      // 更新用户信息
      updateProfile: async (userData: Partial<User>) => {
        set({ isLoading: true, error: null });
        
        try {
          const updatedUser = await AuthService.updateProfile(userData);
          set({
            user: updatedUser,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : '更新用户信息失败',
          });
          throw error;
        }
      },

      // 修改密码
      changePassword: async (data: { current_password: string; new_password: string }) => {
        set({ isLoading: true, error: null });
        
        try {
          await AuthService.changePassword(data);
          set({
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : '密码修改失败',
          });
          throw error;
        }
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      // 检查认证状态
      checkAuth: () => {
        const isAuthenticated = AuthService.isAuthenticated();
        const storedUser = AuthService.getStoredUser();
        
        set({
          user: storedUser,
          isAuthenticated,
        });

        // 如果已认证，设置自动刷新
        if (isAuthenticated) {
          AuthService.setupAutoRefresh();
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// 初始化认证状态
useAuthStore.getState().checkAuth();
