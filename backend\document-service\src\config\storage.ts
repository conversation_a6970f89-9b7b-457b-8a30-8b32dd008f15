import * as Minio from 'minio';
import fs from 'fs-extra';
import path from 'path';
import { config } from '@/config';
import { logger } from '@/utils/logger';
import { StorageProvider } from '@/types/document';

export class MinioStorageProvider implements StorageProvider {
  private client: Minio.Client;
  private bucket: string;

  constructor() {
    this.client = new Minio.Client({
      endPoint: config.storage.minio.endpoint.split(':')[0],
      port: parseInt(config.storage.minio.endpoint.split(':')[1] || '9000', 10),
      useSSL: config.storage.minio.useSSL,
      accessKey: config.storage.minio.accessKey,
      secretKey: config.storage.minio.secretKey,
      region: config.storage.minio.region
    });
    this.bucket = config.storage.minio.bucket;
  }

  async initialize(): Promise<void> {
    try {
      // 检查bucket是否存在，不存在则创建
      const exists = await this.client.bucketExists(this.bucket);
      if (!exists) {
        await this.client.makeBucket(this.bucket, config.storage.minio.region);
        logger.info(`Created MinIO bucket: ${this.bucket}`);
      }
      logger.info('MinIO storage provider initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize MinIO storage provider:', error);
      throw error;
    }
  }

  async upload(file: Buffer, filePath: string, options?: any): Promise<string> {
    try {
      const metadata = options?.metadata || {};
      await this.client.putObject(this.bucket, filePath, file, file.length, metadata);
      logger.debug('File uploaded to MinIO:', { path: filePath, size: file.length });
      return filePath;
    } catch (error) {
      logger.error('Failed to upload file to MinIO:', { path: filePath, error });
      throw error;
    }
  }

  async download(filePath: string): Promise<Buffer> {
    try {
      const stream = await this.client.getObject(this.bucket, filePath);
      const chunks: Buffer[] = [];
      
      return new Promise((resolve, reject) => {
        stream.on('data', (chunk) => chunks.push(chunk));
        stream.on('end', () => resolve(Buffer.concat(chunks)));
        stream.on('error', reject);
      });
    } catch (error) {
      logger.error('Failed to download file from MinIO:', { path: filePath, error });
      throw error;
    }
  }

  async delete(filePath: string): Promise<void> {
    try {
      await this.client.removeObject(this.bucket, filePath);
      logger.debug('File deleted from MinIO:', { path: filePath });
    } catch (error) {
      logger.error('Failed to delete file from MinIO:', { path: filePath, error });
      throw error;
    }
  }

  async exists(filePath: string): Promise<boolean> {
    try {
      await this.client.statObject(this.bucket, filePath);
      return true;
    } catch (error) {
      return false;
    }
  }

  async getUrl(filePath: string, expires: number = 3600): Promise<string> {
    try {
      return await this.client.presignedGetObject(this.bucket, filePath, expires);
    } catch (error) {
      logger.error('Failed to generate presigned URL:', { path: filePath, error });
      throw error;
    }
  }

  async copy(sourcePath: string, destPath: string): Promise<void> {
    try {
      await this.client.copyObject(
        this.bucket,
        destPath,
        `/${this.bucket}/${sourcePath}`
      );
      logger.debug('File copied in MinIO:', { source: sourcePath, dest: destPath });
    } catch (error) {
      logger.error('Failed to copy file in MinIO:', { source: sourcePath, dest: destPath, error });
      throw error;
    }
  }

  async listObjects(prefix: string): Promise<string[]> {
    try {
      const objects: string[] = [];
      const stream = this.client.listObjects(this.bucket, prefix, true);
      
      return new Promise((resolve, reject) => {
        stream.on('data', (obj) => objects.push(obj.name || ''));
        stream.on('end', () => resolve(objects));
        stream.on('error', reject);
      });
    } catch (error) {
      logger.error('Failed to list objects in MinIO:', { prefix, error });
      throw error;
    }
  }
}

export class LocalStorageProvider implements StorageProvider {
  private basePath: string;

  constructor() {
    this.basePath = config.storage.local.uploadPath;
  }

  async initialize(): Promise<void> {
    try {
      await fs.ensureDir(this.basePath);
      await fs.ensureDir(config.storage.local.thumbnailPath);
      logger.info('Local storage provider initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize local storage provider:', error);
      throw error;
    }
  }

  async upload(file: Buffer, filePath: string, options?: any): Promise<string> {
    try {
      const fullPath = path.join(this.basePath, filePath);
      await fs.ensureDir(path.dirname(fullPath));
      await fs.writeFile(fullPath, file);
      logger.debug('File uploaded to local storage:', { path: fullPath, size: file.length });
      return filePath;
    } catch (error) {
      logger.error('Failed to upload file to local storage:', { path: filePath, error });
      throw error;
    }
  }

  async download(filePath: string): Promise<Buffer> {
    try {
      const fullPath = path.join(this.basePath, filePath);
      return await fs.readFile(fullPath);
    } catch (error) {
      logger.error('Failed to download file from local storage:', { path: filePath, error });
      throw error;
    }
  }

  async delete(filePath: string): Promise<void> {
    try {
      const fullPath = path.join(this.basePath, filePath);
      await fs.remove(fullPath);
      logger.debug('File deleted from local storage:', { path: fullPath });
    } catch (error) {
      logger.error('Failed to delete file from local storage:', { path: filePath, error });
      throw error;
    }
  }

  async exists(filePath: string): Promise<boolean> {
    try {
      const fullPath = path.join(this.basePath, filePath);
      return await fs.pathExists(fullPath);
    } catch (error) {
      return false;
    }
  }

  async getUrl(filePath: string, expires?: number): Promise<string> {
    // 本地存储返回相对路径，实际应用中可能需要完整的URL
    return `/files/${filePath}`;
  }

  async copy(sourcePath: string, destPath: string): Promise<void> {
    try {
      const sourceFullPath = path.join(this.basePath, sourcePath);
      const destFullPath = path.join(this.basePath, destPath);
      await fs.ensureDir(path.dirname(destFullPath));
      await fs.copy(sourceFullPath, destFullPath);
      logger.debug('File copied in local storage:', { source: sourcePath, dest: destPath });
    } catch (error) {
      logger.error('Failed to copy file in local storage:', { source: sourcePath, dest: destPath, error });
      throw error;
    }
  }
}

// 存储提供者工厂
export function createStorageProvider(): StorageProvider {
  switch (config.storage.provider) {
    case 'minio':
      return new MinioStorageProvider();
    case 'local':
      return new LocalStorageProvider();
    default:
      throw new Error(`Unsupported storage provider: ${config.storage.provider}`);
  }
}

// 全局存储实例
let storageProvider: StorageProvider;

export async function initializeStorage(): Promise<StorageProvider> {
  if (!storageProvider) {
    storageProvider = createStorageProvider();
    await storageProvider.initialize();
  }
  return storageProvider;
}

export function getStorageProvider(): StorageProvider {
  if (!storageProvider) {
    throw new Error('Storage provider not initialized. Call initializeStorage() first.');
  }
  return storageProvider;
}
