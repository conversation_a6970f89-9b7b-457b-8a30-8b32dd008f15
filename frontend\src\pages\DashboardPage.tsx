import React from 'react';
import { Link } from 'react-router-dom';
import {
  DocumentTextIcon,
  CloudArrowUpIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import { clsx } from 'clsx';

import { useAuthStore } from '@/stores/auth';
import Button from '@/components/ui/Button';

// Mock data - in real app, this would come from API
const stats = [
  {
    name: '总文档数',
    value: '156',
    change: '+12%',
    changeType: 'increase',
    icon: DocumentTextIcon,
  },
  {
    name: '待审批',
    value: '8',
    change: '-2',
    changeType: 'decrease',
    icon: ClockIcon,
  },
  {
    name: '高风险文档',
    value: '3',
    change: '+1',
    changeType: 'increase',
    icon: ExclamationTriangleIcon,
  },
  {
    name: '已完成审批',
    value: '142',
    change: '+18',
    changeType: 'increase',
    icon: CheckCircleIcon,
  },
];

const recentDocuments = [
  {
    id: '1',
    title: '服务协议 - ABC公司',
    status: 'completed',
    riskLevel: 'low',
    uploadedAt: '2024-01-15',
    uploadedBy: '张三',
  },
  {
    id: '2',
    title: '采购合同 - XYZ供应商',
    status: 'processing',
    riskLevel: 'medium',
    uploadedAt: '2024-01-14',
    uploadedBy: '李四',
  },
  {
    id: '3',
    title: '保密协议 - 技术合作',
    status: 'pending',
    riskLevel: 'high',
    uploadedAt: '2024-01-13',
    uploadedBy: '王五',
  },
  {
    id: '4',
    title: '劳动合同 - 新员工入职',
    status: 'completed',
    riskLevel: 'low',
    uploadedAt: '2024-01-12',
    uploadedBy: '赵六',
  },
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-success-100 text-success-800';
    case 'processing':
      return 'bg-warning-100 text-warning-800';
    case 'pending':
      return 'bg-secondary-100 text-secondary-800';
    default:
      return 'bg-secondary-100 text-secondary-800';
  }
};

const getRiskColor = (riskLevel: string) => {
  switch (riskLevel) {
    case 'high':
      return 'text-danger-600';
    case 'medium':
      return 'text-warning-600';
    case 'low':
      return 'text-success-600';
    default:
      return 'text-secondary-600';
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'completed':
      return '已完成';
    case 'processing':
      return '处理中';
    case 'pending':
      return '待处理';
    default:
      return '未知';
  }
};

const getRiskText = (riskLevel: string) => {
  switch (riskLevel) {
    case 'high':
      return '高风险';
    case 'medium':
      return '中风险';
    case 'low':
      return '低风险';
    default:
      return '未知';
  }
};

const DashboardPage: React.FC = () => {
  const { user } = useAuthStore();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-secondary-900 sm:text-3xl sm:truncate">
            欢迎回来，{user?.name}
          </h2>
          <p className="mt-1 text-sm text-secondary-500">
            这是您的文档管理仪表板概览
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <Link to="/documents/upload">
            <Button icon={CloudArrowUpIcon}>
              上传文档
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div
              key={stat.name}
              className="relative bg-white pt-5 px-4 pb-12 sm:pt-6 sm:px-6 shadow rounded-lg overflow-hidden"
            >
              <dt>
                <div className="absolute bg-primary-500 rounded-md p-3">
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <p className="ml-16 text-sm font-medium text-secondary-500 truncate">
                  {stat.name}
                </p>
              </dt>
              <dd className="ml-16 pb-6 flex items-baseline sm:pb-7">
                <p className="text-2xl font-semibold text-secondary-900">
                  {stat.value}
                </p>
                <p
                  className={clsx(
                    'ml-2 flex items-baseline text-sm font-semibold',
                    stat.changeType === 'increase' ? 'text-success-600' : 'text-danger-600'
                  )}
                >
                  {stat.change}
                </p>
              </dd>
            </div>
          );
        })}
      </div>

      {/* Recent Documents */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg leading-6 font-medium text-secondary-900">
              最近文档
            </h3>
            <Link
              to="/documents"
              className="text-sm font-medium text-primary-600 hover:text-primary-500"
            >
              查看全部
            </Link>
          </div>
          
          <div className="flow-root">
            <ul className="-my-5 divide-y divide-secondary-200">
              {recentDocuments.map((document) => (
                <li key={document.id} className="py-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <DocumentTextIcon className="h-8 w-8 text-secondary-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-secondary-900 truncate">
                        {document.title}
                      </p>
                      <p className="text-sm text-secondary-500">
                        {document.uploadedBy} • {document.uploadedAt}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span
                        className={clsx(
                          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                          getStatusColor(document.status)
                        )}
                      >
                        {getStatusText(document.status)}
                      </span>
                      <span className={clsx('text-sm font-medium', getRiskColor(document.riskLevel))}>
                        {getRiskText(document.riskLevel)}
                      </span>
                      <Link to={`/documents/${document.id}`}>
                        <Button variant="ghost" size="sm" icon={EyeIcon}>
                          查看
                        </Button>
                      </Link>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-secondary-900 mb-4">
            快速操作
          </h3>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            <Link
              to="/documents/upload"
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg border border-secondary-200 hover:border-secondary-300"
            >
              <div>
                <span className="rounded-lg inline-flex p-3 bg-primary-50 text-primary-700 ring-4 ring-white">
                  <CloudArrowUpIcon className="h-6 w-6" />
                </span>
              </div>
              <div className="mt-8">
                <h3 className="text-lg font-medium">
                  <span className="absolute inset-0" />
                  上传新文档
                </h3>
                <p className="mt-2 text-sm text-secondary-500">
                  上传合同、协议等文档进行AI分析
                </p>
              </div>
            </Link>

            <Link
              to="/documents?status=pending"
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg border border-secondary-200 hover:border-secondary-300"
            >
              <div>
                <span className="rounded-lg inline-flex p-3 bg-warning-50 text-warning-700 ring-4 ring-white">
                  <ClockIcon className="h-6 w-6" />
                </span>
              </div>
              <div className="mt-8">
                <h3 className="text-lg font-medium">
                  <span className="absolute inset-0" />
                  待审批文档
                </h3>
                <p className="mt-2 text-sm text-secondary-500">
                  查看需要您审批的文档
                </p>
              </div>
            </Link>

            <Link
              to="/documents?risk=high"
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg border border-secondary-200 hover:border-secondary-300"
            >
              <div>
                <span className="rounded-lg inline-flex p-3 bg-danger-50 text-danger-700 ring-4 ring-white">
                  <ExclamationTriangleIcon className="h-6 w-6" />
                </span>
              </div>
              <div className="mt-8">
                <h3 className="text-lg font-medium">
                  <span className="absolute inset-0" />
                  高风险文档
                </h3>
                <p className="mt-2 text-sm text-secondary-500">
                  查看AI识别的高风险文档
                </p>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
