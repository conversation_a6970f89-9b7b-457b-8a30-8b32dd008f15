from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form
from typing import List, Optional
import tempfile
import os

from app.models.schemas import (
    OCRRequest, OCRResponse, BaseResponse
)
from app.services.ocr_service import ocr_service
from app.core.logging import logger
from app.core.exceptions import OCRError, FileNotFoundError, UnsupportedFileTypeError

router = APIRouter()

@router.post("/process", response_model=OCRResponse)
async def process_ocr(
    file: UploadFile = File(...),
    document_id: str = Form(...),
    languages: Optional[str] = Form("eng,chi_sim"),
    confidence_threshold: Optional[float] = Form(0.6),
    preprocess: Optional[bool] = Form(True)
):
    """
    Process document for OCR text extraction
    
    - **file**: Document file to process (PDF, images)
    - **document_id**: Unique document identifier
    - **languages**: OCR languages (comma-separated)
    - **confidence_threshold**: Minimum confidence threshold (0.0-1.0)
    - **preprocess**: Whether to preprocess images for better OCR
    """
    temp_file_path = None
    
    try:
        # Validate file type
        allowed_types = [
            "application/pdf",
            "image/jpeg",
            "image/png", 
            "image/tiff",
            "image/bmp"
        ]
        
        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file type: {file.content_type}"
            )
        
        # Save uploaded file to temporary location
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        # Parse languages
        language_list = [lang.strip() for lang in languages.split(",")]
        
        # Create OCR request
        ocr_request = OCRRequest(
            document_id=document_id,
            file_path=temp_file_path,
            languages=language_list,
            confidence_threshold=confidence_threshold,
            preprocess=preprocess
        )
        
        # Process OCR
        result = await ocr_service.process_document(ocr_request)
        
        logger.info(f"OCR processing completed for document {document_id}")
        
        return OCRResponse(
            success=True,
            data=result,
            message="OCR processing completed successfully"
        )
        
    except UnsupportedFileTypeError as e:
        logger.error(f"Unsupported file type for OCR: {e}")
        raise HTTPException(status_code=400, detail=str(e))
        
    except OCRError as e:
        logger.error(f"OCR processing failed: {e}")
        raise HTTPException(status_code=422, detail=str(e))
        
    except Exception as e:
        logger.error(f"Unexpected error in OCR processing: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
        
    finally:
        # Clean up temporary file
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                logger.warning(f"Failed to clean up temp file {temp_file_path}: {e}")

@router.get("/languages")
async def get_supported_languages():
    """
    Get list of supported OCR languages
    """
    try:
        # This would typically come from configuration or model capabilities
        supported_languages = [
            {"code": "eng", "name": "English"},
            {"code": "chi_sim", "name": "Chinese Simplified"},
            {"code": "chi_tra", "name": "Chinese Traditional"},
            {"code": "fra", "name": "French"},
            {"code": "deu", "name": "German"},
            {"code": "spa", "name": "Spanish"},
            {"code": "jpn", "name": "Japanese"},
            {"code": "kor", "name": "Korean"},
        ]
        
        return BaseResponse(
            success=True,
            data={"languages": supported_languages},
            message="Supported languages retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error getting supported languages: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/config")
async def get_ocr_config():
    """
    Get current OCR configuration
    """
    try:
        from app.config import settings, get_model_config
        
        ocr_config = get_model_config("ocr", "tesseract")
        
        config_info = {
            "enabled": settings.ocr_enabled,
            "languages": settings.ocr_languages,
            "confidence_threshold": settings.ocr_confidence_threshold,
            "tesseract_config": ocr_config.get("config") if ocr_config else None,
            "max_file_size": "100MB",  # From settings
            "supported_formats": ["PDF", "JPEG", "PNG", "TIFF", "BMP"]
        }
        
        return BaseResponse(
            success=True,
            data=config_info,
            message="OCR configuration retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error getting OCR config: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/batch", response_model=BaseResponse)
async def process_batch_ocr(
    files: List[UploadFile] = File(...),
    document_ids: str = Form(...),
    languages: Optional[str] = Form("eng,chi_sim"),
    confidence_threshold: Optional[float] = Form(0.6),
    preprocess: Optional[bool] = Form(True)
):
    """
    Process multiple documents for OCR in batch
    
    - **files**: List of document files to process
    - **document_ids**: Comma-separated list of document IDs
    - **languages**: OCR languages (comma-separated)
    - **confidence_threshold**: Minimum confidence threshold (0.0-1.0)
    - **preprocess**: Whether to preprocess images for better OCR
    """
    try:
        # Parse document IDs
        doc_id_list = [doc_id.strip() for doc_id in document_ids.split(",")]
        
        if len(files) != len(doc_id_list):
            raise HTTPException(
                status_code=400,
                detail="Number of files must match number of document IDs"
            )
        
        # Parse languages
        language_list = [lang.strip() for lang in languages.split(",")]
        
        results = []
        errors = []
        
        for i, (file, doc_id) in enumerate(zip(files, doc_id_list)):
            temp_file_path = None
            try:
                # Save uploaded file to temporary location
                with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
                    content = await file.read()
                    temp_file.write(content)
                    temp_file_path = temp_file.name
                
                # Create OCR request
                ocr_request = OCRRequest(
                    document_id=doc_id,
                    file_path=temp_file_path,
                    languages=language_list,
                    confidence_threshold=confidence_threshold,
                    preprocess=preprocess
                )
                
                # Process OCR
                result = await ocr_service.process_document(ocr_request)
                results.append({
                    "document_id": doc_id,
                    "status": "success",
                    "result": result
                })
                
            except Exception as e:
                logger.error(f"OCR processing failed for document {doc_id}: {e}")
                errors.append({
                    "document_id": doc_id,
                    "status": "error",
                    "error": str(e)
                })
                
            finally:
                # Clean up temporary file
                if temp_file_path and os.path.exists(temp_file_path):
                    try:
                        os.unlink(temp_file_path)
                    except Exception as e:
                        logger.warning(f"Failed to clean up temp file {temp_file_path}: {e}")
        
        return BaseResponse(
            success=len(errors) == 0,
            data={
                "results": results,
                "errors": errors,
                "total_processed": len(results),
                "total_errors": len(errors)
            },
            message=f"Batch OCR processing completed. {len(results)} successful, {len(errors)} failed."
        )
        
    except Exception as e:
        logger.error(f"Batch OCR processing failed: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
