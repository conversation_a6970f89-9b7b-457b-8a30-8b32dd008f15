from datetime import datetime
from typing import Optional, Dict, Any

class AIServiceException(Exception):
    """Base exception for AI Service"""
    
    def __init__(
        self,
        detail: str,
        status_code: int = 500,
        error_code: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.detail = detail
        self.status_code = status_code
        self.error_code = error_code or self.__class__.__name__
        self.metadata = metadata or {}
        self.timestamp = datetime.utcnow()
        super().__init__(self.detail)

class ValidationError(AIServiceException):
    """Validation error"""
    def __init__(self, detail: str, metadata: Optional[Dict[str, Any]] = None):
        super().__init__(detail, 400, "VALIDATION_ERROR", metadata)

class AuthenticationError(AIServiceException):
    """Authentication error"""
    def __init__(self, detail: str = "Authentication failed", metadata: Optional[Dict[str, Any]] = None):
        super().__init__(detail, 401, "AUTHENTICATION_ERROR", metadata)

class AuthorizationError(AIServiceException):
    """Authorization error"""
    def __init__(self, detail: str = "Access denied", metadata: Optional[Dict[str, Any]] = None):
        super().__init__(detail, 403, "AUTHORIZATION_ERROR", metadata)

class NotFoundError(AIServiceException):
    """Resource not found error"""
    def __init__(self, detail: str = "Resource not found", metadata: Optional[Dict[str, Any]] = None):
        super().__init__(detail, 404, "NOT_FOUND_ERROR", metadata)

class ConflictError(AIServiceException):
    """Resource conflict error"""
    def __init__(self, detail: str = "Resource conflict", metadata: Optional[Dict[str, Any]] = None):
        super().__init__(detail, 409, "CONFLICT_ERROR", metadata)

class RateLimitError(AIServiceException):
    """Rate limit exceeded error"""
    def __init__(self, detail: str = "Rate limit exceeded", metadata: Optional[Dict[str, Any]] = None):
        super().__init__(detail, 429, "RATE_LIMIT_ERROR", metadata)

class ModelError(AIServiceException):
    """AI model related error"""
    def __init__(self, detail: str, metadata: Optional[Dict[str, Any]] = None):
        super().__init__(detail, 500, "MODEL_ERROR", metadata)

class ModelNotLoadedError(ModelError):
    """Model not loaded error"""
    def __init__(self, model_name: str, metadata: Optional[Dict[str, Any]] = None):
        detail = f"Model '{model_name}' is not loaded"
        super().__init__(detail, {"model_name": model_name, **(metadata or {})})

class ModelLoadError(ModelError):
    """Model loading error"""
    def __init__(self, model_name: str, reason: str, metadata: Optional[Dict[str, Any]] = None):
        detail = f"Failed to load model '{model_name}': {reason}"
        super().__init__(detail, {"model_name": model_name, "reason": reason, **(metadata or {})})

class ProcessingError(AIServiceException):
    """Document processing error"""
    def __init__(self, detail: str, metadata: Optional[Dict[str, Any]] = None):
        super().__init__(detail, 422, "PROCESSING_ERROR", metadata)

class OCRError(ProcessingError):
    """OCR processing error"""
    def __init__(self, detail: str, metadata: Optional[Dict[str, Any]] = None):
        super().__init__(f"OCR processing failed: {detail}", {"error_type": "ocr", **(metadata or {})})

class NLPError(ProcessingError):
    """NLP processing error"""
    def __init__(self, detail: str, metadata: Optional[Dict[str, Any]] = None):
        super().__init__(f"NLP processing failed: {detail}", {"error_type": "nlp", **(metadata or {})})

class RiskAnalysisError(ProcessingError):
    """Risk analysis error"""
    def __init__(self, detail: str, metadata: Optional[Dict[str, Any]] = None):
        super().__init__(f"Risk analysis failed: {detail}", {"error_type": "risk_analysis", **(metadata or {})})

class ContractAnalysisError(ProcessingError):
    """Contract analysis error"""
    def __init__(self, detail: str, metadata: Optional[Dict[str, Any]] = None):
        super().__init__(f"Contract analysis failed: {detail}", {"error_type": "contract_analysis", **(metadata or {})})

class FileError(AIServiceException):
    """File related error"""
    def __init__(self, detail: str, metadata: Optional[Dict[str, Any]] = None):
        super().__init__(detail, 400, "FILE_ERROR", metadata)

class FileNotFoundError(FileError):
    """File not found error"""
    def __init__(self, file_path: str, metadata: Optional[Dict[str, Any]] = None):
        detail = f"File not found: {file_path}"
        super().__init__(detail, {"file_path": file_path, **(metadata or {})})

class UnsupportedFileTypeError(FileError):
    """Unsupported file type error"""
    def __init__(self, file_type: str, metadata: Optional[Dict[str, Any]] = None):
        detail = f"Unsupported file type: {file_type}"
        super().__init__(detail, {"file_type": file_type, **(metadata or {})})

class FileTooLargeError(FileError):
    """File too large error"""
    def __init__(self, file_size: int, max_size: int, metadata: Optional[Dict[str, Any]] = None):
        detail = f"File size {file_size} bytes exceeds maximum allowed size {max_size} bytes"
        super().__init__(detail, {"file_size": file_size, "max_size": max_size, **(metadata or {})})

class DatabaseError(AIServiceException):
    """Database related error"""
    def __init__(self, detail: str, metadata: Optional[Dict[str, Any]] = None):
        super().__init__(detail, 500, "DATABASE_ERROR", metadata)

class CacheError(AIServiceException):
    """Cache related error"""
    def __init__(self, detail: str, metadata: Optional[Dict[str, Any]] = None):
        super().__init__(detail, 500, "CACHE_ERROR", metadata)

class ExternalServiceError(AIServiceException):
    """External service error"""
    def __init__(self, service_name: str, detail: str, metadata: Optional[Dict[str, Any]] = None):
        full_detail = f"External service '{service_name}' error: {detail}"
        super().__init__(full_detail, 502, "EXTERNAL_SERVICE_ERROR", 
                        {"service_name": service_name, **(metadata or {})})

class TaskError(AIServiceException):
    """Task processing error"""
    def __init__(self, detail: str, metadata: Optional[Dict[str, Any]] = None):
        super().__init__(detail, 500, "TASK_ERROR", metadata)

class TaskNotFoundError(TaskError):
    """Task not found error"""
    def __init__(self, task_id: str, metadata: Optional[Dict[str, Any]] = None):
        detail = f"Task not found: {task_id}"
        super().__init__(detail, {"task_id": task_id, **(metadata or {})})

class TaskTimeoutError(TaskError):
    """Task timeout error"""
    def __init__(self, task_id: str, timeout: int, metadata: Optional[Dict[str, Any]] = None):
        detail = f"Task {task_id} timed out after {timeout} seconds"
        super().__init__(detail, {"task_id": task_id, "timeout": timeout, **(metadata or {})})

class ConfigurationError(AIServiceException):
    """Configuration error"""
    def __init__(self, detail: str, metadata: Optional[Dict[str, Any]] = None):
        super().__init__(detail, 500, "CONFIGURATION_ERROR", metadata)

class ResourceExhaustedError(AIServiceException):
    """Resource exhausted error"""
    def __init__(self, resource_type: str, detail: str, metadata: Optional[Dict[str, Any]] = None):
        full_detail = f"Resource exhausted ({resource_type}): {detail}"
        super().__init__(full_detail, 503, "RESOURCE_EXHAUSTED_ERROR", 
                        {"resource_type": resource_type, **(metadata or {})})

class MemoryError(ResourceExhaustedError):
    """Memory exhausted error"""
    def __init__(self, detail: str = "Insufficient memory", metadata: Optional[Dict[str, Any]] = None):
        super().__init__("memory", detail, metadata)

class CPUError(ResourceExhaustedError):
    """CPU exhausted error"""
    def __init__(self, detail: str = "CPU resources exhausted", metadata: Optional[Dict[str, Any]] = None):
        super().__init__("cpu", detail, metadata)

class DiskSpaceError(ResourceExhaustedError):
    """Disk space exhausted error"""
    def __init__(self, detail: str = "Insufficient disk space", metadata: Optional[Dict[str, Any]] = None):
        super().__init__("disk", detail, metadata)

# Exception mapping for common errors
EXCEPTION_MAPPING = {
    "validation": ValidationError,
    "authentication": AuthenticationError,
    "authorization": AuthorizationError,
    "not_found": NotFoundError,
    "conflict": ConflictError,
    "rate_limit": RateLimitError,
    "model": ModelError,
    "processing": ProcessingError,
    "ocr": OCRError,
    "nlp": NLPError,
    "risk_analysis": RiskAnalysisError,
    "contract_analysis": ContractAnalysisError,
    "file": FileError,
    "database": DatabaseError,
    "cache": CacheError,
    "external_service": ExternalServiceError,
    "task": TaskError,
    "configuration": ConfigurationError,
    "resource_exhausted": ResourceExhaustedError
}

def create_exception(exception_type: str, detail: str, **kwargs) -> AIServiceException:
    """Factory function to create exceptions"""
    exception_class = EXCEPTION_MAPPING.get(exception_type, AIServiceException)
    return exception_class(detail, **kwargs)
