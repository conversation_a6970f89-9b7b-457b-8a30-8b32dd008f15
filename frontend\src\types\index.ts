// User types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar_url?: string;
  phone?: string;
  status: 'active' | 'inactive' | 'suspended';
  email_verified: boolean;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
  organization?: Organization;
  role?: OrganizationRole;
}

export interface Organization {
  id: string;
  name: string;
  description?: string;
  logo_url?: string;
  website?: string;
  created_at: string;
  updated_at: string;
}

export enum OrganizationRole {
  OWNER = 'owner',
  ADMIN = 'admin',
  MANAGER = 'manager',
  MEMBER = 'member',
  VIEWER = 'viewer',
}

// Document types
export interface Document {
  id: string;
  title: string;
  description?: string;
  file_path: string;
  file_name: string;
  file_size: number;
  file_type: string;
  mime_type: string;
  organization_id: string;
  created_by: string;
  status: DocumentStatus;
  tags: string[];
  metadata: DocumentMetadata;
  created_at: string;
  updated_at: string;
  analysis_result?: DocumentAnalysisResult;
  risk_analysis?: RiskAnalysisResult;
}

export enum DocumentStatus {
  UPLOADING = 'uploading',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  ARCHIVED = 'archived',
}

export interface DocumentMetadata {
  pages?: number;
  word_count?: number;
  character_count?: number;
  language?: string;
  author?: string;
  subject?: string;
  keywords?: string[];
  creation_date?: string;
  modification_date?: string;
  application?: string;
  format_version?: string;
  encryption?: boolean;
  digital_signature?: boolean;
  [key: string]: any;
}

export interface DocumentVersion {
  id: string;
  document_id: string;
  version_number: number;
  file_path: string;
  file_size: number;
  changes_summary?: string;
  created_by: string;
  created_at: string;
}

// AI Analysis types
export interface DocumentAnalysisResult {
  document_id: string;
  content: {
    text: string;
    pages: DocumentPage[];
    tables: DocumentTable[];
    images: DocumentImage[];
  };
  metadata: DocumentMetadata;
  analysis: {
    language: string;
    confidence: number;
    entities: DocumentEntity[];
    keywords: string[];
    summary?: string;
  };
  created_at: string;
}

export interface DocumentPage {
  page_number: number;
  text: string;
  bbox?: BoundingBox;
  confidence?: number;
}

export interface DocumentTable {
  page_number: number;
  rows: string[][];
  bbox?: BoundingBox;
  confidence?: number;
}

export interface DocumentImage {
  page_number: number;
  image_path: string;
  bbox?: BoundingBox;
  description?: string;
}

export interface DocumentEntity {
  text: string;
  label: string;
  confidence: number;
  start: number;
  end: number;
}

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

// Risk Analysis types
export interface RiskAnalysisResult {
  document_id: string;
  overall_risk_level: RiskLevel;
  overall_risk_score: number;
  risk_factors: RiskFactor[];
  compliance_checks: ComplianceCheck[];
  recommendations: string[];
  processing_time: number;
  metadata: Record<string, any>;
}

export enum RiskLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface RiskFactor {
  category: string;
  description: string;
  risk_level: RiskLevel;
  confidence: number;
  location?: string;
  recommendation?: string;
}

export interface ComplianceCheck {
  regulation: string;
  status: 'compliant' | 'non_compliant' | 'unclear';
  confidence: number;
  details?: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

// Form types
export interface LoginForm {
  email: string;
  password: string;
  remember_me?: boolean;
}

export interface RegisterForm {
  email: string;
  password: string;
  name: string;
  phone?: string;
}

export interface DocumentUploadForm {
  title: string;
  description?: string;
  tags?: string[];
  organization_id: string;
  file: File;
}

export interface DocumentUpdateForm {
  title?: string;
  description?: string;
  tags?: string[];
}

// Search and Filter types
export interface DocumentSearchParams {
  query?: string;
  tags?: string[];
  file_type?: string;
  status?: DocumentStatus;
  created_by?: string;
  organization_id?: string;
  date_from?: string;
  date_to?: string;
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface FilterOption {
  label: string;
  value: string;
  count?: number;
}

// UI State types
export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

export interface UploadProgress {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
}

// Navigation types
export interface NavItem {
  name: string;
  href: string;
  icon?: React.ComponentType<any>;
  current?: boolean;
  children?: NavItem[];
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';

// Notification types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// Table types
export interface TableColumn<T = any> {
  key: string;
  title: string;
  dataIndex?: string;
  render?: (value: any, record: T, index: number) => React.ReactNode;
  sorter?: boolean;
  width?: number;
  align?: 'left' | 'center' | 'right';
  fixed?: 'left' | 'right';
}

export interface TableProps<T = any> {
  columns: TableColumn<T>[];
  data: T[];
  loading?: boolean;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  rowKey?: string | ((record: T) => string);
  onRow?: (record: T, index: number) => any;
  scroll?: { x?: number; y?: number };
}

// Chart types
export interface ChartData {
  name: string;
  value: number;
  color?: string;
}

export interface TimeSeriesData {
  date: string;
  value: number;
  category?: string;
}

// File types
export interface FileInfo {
  name: string;
  size: number;
  type: string;
  lastModified: number;
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// Settings types
export interface UserSettings {
  theme: Theme;
  language: string;
  notifications: {
    email: boolean;
    push: boolean;
    desktop: boolean;
  };
  privacy: {
    profile_visibility: 'public' | 'private' | 'organization';
    activity_tracking: boolean;
  };
}

// Analytics types
export interface AnalyticsData {
  total_documents: number;
  documents_by_status: Record<DocumentStatus, number>;
  documents_by_type: Record<string, number>;
  risk_distribution: Record<RiskLevel, number>;
  upload_trends: TimeSeriesData[];
  top_uploaders: Array<{
    user_id: string;
    user_name: string;
    count: number;
  }>;
}
