{"name": "doc<PERSON>", "version": "1.0.0", "description": "DocMind - 合同智能审阅 SaaS 服务", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\" \"npm run dev:ai\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "dev:ai": "cd ai-service && python -m uvicorn main:app --reload --port 8001", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "test": "npm run test:frontend && npm run test:backend && npm run test:ai", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "test:ai": "cd ai-service && python -m pytest", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "k8s:deploy": "kubectl apply -f infrastructure/kubernetes/", "setup": "npm run setup:frontend && npm run setup:backend && npm run setup:ai", "setup:frontend": "cd frontend && npm install", "setup:backend": "cd backend && npm install", "setup:ai": "cd ai-service && pip install -r requirements.txt"}, "keywords": ["contract", "ai", "legal", "saas", "nlp", "document-analysis"], "author": "DocMind Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{py}": ["black", "flake8"]}, "workspaces": ["frontend", "backend/*"]}