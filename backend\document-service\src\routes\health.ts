import express from 'express';
import { checkDatabaseHealth } from '../config/database';
import { checkStorageHealth } from '../config/storage';
import { logger } from '../utils/logger';

const router = express.Router();

/**
 * @swagger
 * /health:
 *   get:
 *     summary: Health check endpoint
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Service is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: healthy
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 uptime:
 *                   type: number
 *                 version:
 *                   type: string
 *                 checks:
 *                   type: object
 *                   properties:
 *                     database:
 *                       type: object
 *                     storage:
 *                       type: object
 *       503:
 *         description: Service is unhealthy
 */
router.get('/', async (req, res) => {
  const startTime = Date.now();
  const checks: any = {};
  let overallStatus = 'healthy';

  try {
    // Check database health
    try {
      const dbHealth = await checkDatabaseHealth();
      checks.database = {
        status: dbHealth.postgres && dbHealth.mongodb && dbHealth.redis ? 'healthy' : 'unhealthy',
        details: dbHealth,
        responseTime: Date.now() - startTime,
      };
      
      if (!checks.database.status || checks.database.status !== 'healthy') {
        overallStatus = 'unhealthy';
      }
    } catch (error) {
      checks.database = {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime: Date.now() - startTime,
      };
      overallStatus = 'unhealthy';
    }

    // Check storage health
    try {
      const storageHealth = await checkStorageHealth();
      checks.storage = {
        status: storageHealth ? 'healthy' : 'unhealthy',
        responseTime: Date.now() - startTime,
      };
      
      if (!storageHealth) {
        overallStatus = 'unhealthy';
      }
    } catch (error) {
      checks.storage = {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime: Date.now() - startTime,
      };
      overallStatus = 'unhealthy';
    }

    // Memory usage
    const memoryUsage = process.memoryUsage();
    checks.memory = {
      status: 'healthy',
      usage: {
        rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB',
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
        external: Math.round(memoryUsage.external / 1024 / 1024) + ' MB',
      },
    };

    const response = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.0.0',
      service: 'document-service',
      checks,
      responseTime: Date.now() - startTime,
    };

    const statusCode = overallStatus === 'healthy' ? 200 : 503;
    
    if (overallStatus !== 'healthy') {
      logger.warn('Health check failed', response);
    }

    res.status(statusCode).json(response);
  } catch (error) {
    logger.error('Health check error:', error);
    
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.0.0',
      service: 'document-service',
      error: error instanceof Error ? error.message : 'Unknown error',
      responseTime: Date.now() - startTime,
    });
  }
});

/**
 * @swagger
 * /health/ready:
 *   get:
 *     summary: Readiness check endpoint
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Service is ready
 *       503:
 *         description: Service is not ready
 */
router.get('/ready', async (req, res) => {
  try {
    // Check if all critical services are available
    const dbHealth = await checkDatabaseHealth();
    const storageHealth = await checkStorageHealth();
    
    const isReady = dbHealth.postgres && dbHealth.mongodb && dbHealth.redis && storageHealth;
    
    if (isReady) {
      res.status(200).json({
        status: 'ready',
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(503).json({
        status: 'not ready',
        timestamp: new Date().toISOString(),
        details: {
          database: dbHealth,
          storage: storageHealth,
        },
      });
    }
  } catch (error) {
    logger.error('Readiness check error:', error);
    res.status(503).json({
      status: 'not ready',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * @swagger
 * /health/live:
 *   get:
 *     summary: Liveness check endpoint
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: Service is alive
 */
router.get('/live', (req, res) => {
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

export default router;
