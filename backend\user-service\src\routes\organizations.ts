import { Router } from 'express';
import { authenticateToken, validateUser, requireRole } from '@/middleware/auth';
import { OrganizationRole } from '@/types/user';

const router = Router();

// 所有组织路由都需要认证
router.use(authenticateToken);
router.use(validateUser);

/**
 * @swagger
 * /api/organizations:
 *   get:
 *     summary: 获取组织列表
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 组织列表获取成功
 *       401:
 *         description: 未认证
 */
router.get('/', (req, res) => {
  res.json({
    success: true,
    data: [],
    message: 'Organizations endpoint - to be implemented'
  });
});

/**
 * @swagger
 * /api/organizations:
 *   post:
 *     summary: 创建组织
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       201:
 *         description: 组织创建成功
 *       401:
 *         description: 未认证
 */
router.post('/', (req, res) => {
  res.status(201).json({
    success: true,
    message: 'Create organization endpoint - to be implemented'
  });
});

/**
 * @swagger
 * /api/organizations/{id}:
 *   get:
 *     summary: 获取组织详情
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: 组织详情获取成功
 *       401:
 *         description: 未认证
 *       404:
 *         description: 组织不存在
 */
router.get('/:id', (req, res) => {
  res.json({
    success: true,
    message: 'Get organization endpoint - to be implemented'
  });
});

/**
 * @swagger
 * /api/organizations/{id}:
 *   put:
 *     summary: 更新组织信息
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: 组织信息更新成功
 *       401:
 *         description: 未认证
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 组织不存在
 */
router.put('/:id',
  requireRole(OrganizationRole.ADMIN, OrganizationRole.OWNER),
  (req, res) => {
    res.json({
      success: true,
      message: 'Update organization endpoint - to be implemented'
    });
  }
);

/**
 * @swagger
 * /api/organizations/{id}/members:
 *   get:
 *     summary: 获取组织成员列表
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: 成员列表获取成功
 *       401:
 *         description: 未认证
 *       403:
 *         description: 权限不足
 */
router.get('/:id/members', (req, res) => {
  res.json({
    success: true,
    data: [],
    message: 'Get organization members endpoint - to be implemented'
  });
});

/**
 * @swagger
 * /api/organizations/{id}/invite:
 *   post:
 *     summary: 邀请用户加入组织
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: 邀请发送成功
 *       401:
 *         description: 未认证
 *       403:
 *         description: 权限不足
 */
router.post('/:id/invite',
  requireRole(OrganizationRole.ADMIN, OrganizationRole.OWNER),
  (req, res) => {
    res.json({
      success: true,
      message: 'Invite user endpoint - to be implemented'
    });
  }
);

export { router as organizationRoutes };
