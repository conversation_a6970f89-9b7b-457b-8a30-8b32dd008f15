export interface Document {
  id: string;
  title: string;
  description?: string;
  file_path: string;
  file_name: string;
  file_size: number;
  file_type: string;
  mime_type: string;
  organization_id: string;
  created_by: string;
  status: DocumentStatus;
  tags: string[];
  metadata: DocumentMetadata;
  created_at: Date;
  updated_at: Date;
}

export interface DocumentVersion {
  id: string;
  document_id: string;
  version_number: number;
  file_path: string;
  file_size: number;
  changes_summary?: string;
  created_by: string;
  created_at: Date;
}

export interface DocumentPermission {
  id: string;
  document_id: string;
  user_id: string;
  permission: DocumentPermissionType;
  granted_by: string;
  granted_at: Date;
}

export interface DocumentMetadata {
  pages?: number;
  word_count?: number;
  character_count?: number;
  language?: string;
  author?: string;
  subject?: string;
  keywords?: string[];
  creation_date?: Date;
  modification_date?: Date;
  application?: string;
  format_version?: string;
  encryption?: boolean;
  digital_signature?: boolean;
  [key: string]: any;
}

export interface CreateDocumentRequest {
  title: string;
  description?: string;
  tags?: string[];
  organization_id: string;
}

export interface UpdateDocumentRequest {
  title?: string;
  description?: string;
  tags?: string[];
}

export interface UploadDocumentRequest extends CreateDocumentRequest {
  file: Express.Multer.File;
}

export interface DocumentSearchQuery {
  query?: string;
  tags?: string[];
  file_type?: string;
  status?: DocumentStatus;
  created_by?: string;
  organization_id?: string;
  date_from?: Date;
  date_to?: Date;
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface DocumentAnalysisResult {
  document_id: string;
  content: {
    text: string;
    pages: DocumentPage[];
    tables: DocumentTable[];
    images: DocumentImage[];
  };
  metadata: DocumentMetadata;
  analysis: {
    language: string;
    confidence: number;
    entities: DocumentEntity[];
    keywords: string[];
    summary?: string;
  };
  created_at: Date;
}

export interface DocumentPage {
  page_number: number;
  text: string;
  bbox?: BoundingBox;
  confidence?: number;
}

export interface DocumentTable {
  page_number: number;
  rows: string[][];
  bbox?: BoundingBox;
  confidence?: number;
}

export interface DocumentImage {
  page_number: number;
  image_path: string;
  bbox?: BoundingBox;
  description?: string;
}

export interface DocumentEntity {
  text: string;
  label: string;
  confidence: number;
  start: number;
  end: number;
}

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface DocumentShare {
  id: string;
  document_id: string;
  shared_by: string;
  shared_with?: string;
  share_token: string;
  permissions: DocumentPermissionType[];
  expires_at?: Date;
  password_protected: boolean;
  download_allowed: boolean;
  view_count: number;
  created_at: Date;
}

export interface CreateShareRequest {
  document_id: string;
  shared_with?: string;
  permissions: DocumentPermissionType[];
  expires_at?: Date;
  password?: string;
  download_allowed?: boolean;
}

export interface DocumentComment {
  id: string;
  document_id: string;
  user_id: string;
  content: string;
  page_number?: number;
  position?: {
    x: number;
    y: number;
  };
  parent_id?: string;
  created_at: Date;
  updated_at: Date;
}

export interface CreateCommentRequest {
  document_id: string;
  content: string;
  page_number?: number;
  position?: {
    x: number;
    y: number;
  };
  parent_id?: string;
}

export interface DocumentActivity {
  id: string;
  document_id: string;
  user_id: string;
  action: DocumentAction;
  details: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  created_at: Date;
}

export enum DocumentStatus {
  UPLOADING = 'uploading',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  ARCHIVED = 'archived'
}

export enum DocumentPermissionType {
  READ = 'read',
  WRITE = 'write',
  ADMIN = 'admin'
}

export enum DocumentAction {
  UPLOAD = 'upload',
  VIEW = 'view',
  DOWNLOAD = 'download',
  UPDATE = 'update',
  DELETE = 'delete',
  SHARE = 'share',
  COMMENT = 'comment',
  VERSION_CREATE = 'version_create',
  PERMISSION_GRANT = 'permission_grant',
  PERMISSION_REVOKE = 'permission_revoke'
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Record<string, string[]>;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

export interface FileUploadConfig {
  maxFileSize: number;
  allowedMimeTypes: string[];
  allowedExtensions: string[];
  uploadPath: string;
  thumbnailPath: string;
}

export interface StorageProvider {
  upload(file: Buffer, path: string, options?: any): Promise<string>;
  download(path: string): Promise<Buffer>;
  delete(path: string): Promise<void>;
  exists(path: string): Promise<boolean>;
  getUrl(path: string, expires?: number): Promise<string>;
  copy(sourcePath: string, destPath: string): Promise<void>;
}

export interface DocumentProcessor {
  process(filePath: string, options?: any): Promise<DocumentAnalysisResult>;
  extractText(filePath: string): Promise<string>;
  extractMetadata(filePath: string): Promise<DocumentMetadata>;
  generateThumbnail(filePath: string, outputPath: string): Promise<void>;
}

export interface SearchResult {
  document: Document;
  score: number;
  highlights: string[];
  snippet: string;
}

export interface DocumentStats {
  total_documents: number;
  total_size: number;
  by_status: Record<DocumentStatus, number>;
  by_type: Record<string, number>;
  by_month: Array<{
    month: string;
    count: number;
    size: number;
  }>;
  top_uploaders: Array<{
    user_id: string;
    count: number;
    size: number;
  }>;
}
