#!/bin/bash

# DocMind 开发环境设置脚本

set -e

echo "🚀 DocMind 开发环境设置开始..."

# 检查必要的工具
check_requirements() {
    echo "📋 检查系统要求..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 18 ]; then
        echo "❌ Node.js 版本过低，需要 18+，当前版本: $(node -v)"
        exit 1
    fi
    echo "✅ Node.js $(node -v)"
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python3 未安装，请先安装 Python 3.9+"
        exit 1
    fi
    echo "✅ Python $(python3 --version)"
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker 未安装，请先安装 Docker"
        exit 1
    fi
    echo "✅ Docker $(docker --version)"
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    echo "✅ Docker Compose $(docker-compose --version)"
}

# 安装依赖
install_dependencies() {
    echo "📦 安装项目依赖..."
    
    # 安装根目录依赖
    echo "安装根目录依赖..."
    npm install
    
    # 安装前端依赖
    if [ -d "frontend" ]; then
        echo "安装前端依赖..."
        cd frontend && npm install && cd ..
    fi
    
    # 安装后端服务依赖
    for service in backend/*/; do
        if [ -f "$service/package.json" ]; then
            echo "安装 $service 依赖..."
            cd "$service" && npm install && cd ../..
        fi
    done
    
    # 安装AI服务依赖
    if [ -f "ai-service/requirements.txt" ]; then
        echo "安装AI服务依赖..."
        cd ai-service
        if command -v pip3 &> /dev/null; then
            pip3 install -r requirements.txt
        else
            pip install -r requirements.txt
        fi
        cd ..
    fi
}

# 设置环境变量
setup_environment() {
    echo "🔧 设置环境变量..."
    
    # 复制开发环境配置
    if [ ! -f ".env" ]; then
        cp config/development.env .env
        echo "✅ 创建 .env 文件"
    else
        echo "⚠️  .env 文件已存在，跳过创建"
    fi
    
    # 为各个服务创建环境配置
    for service in backend/*/; do
        if [ ! -f "$service/.env" ]; then
            cp config/development.env "$service/.env"
            echo "✅ 为 $service 创建环境配置"
        fi
    done
    
    if [ -d "ai-service" ] && [ ! -f "ai-service/.env" ]; then
        cp config/development.env ai-service/.env
        echo "✅ 为 AI 服务创建环境配置"
    fi
}

# 启动基础服务
start_infrastructure() {
    echo "🐳 启动基础设施服务..."
    
    # 创建必要的目录
    mkdir -p logs
    mkdir -p data/postgres
    mkdir -p data/mongodb
    mkdir -p data/redis
    mkdir -p data/elasticsearch
    mkdir -p data/minio
    
    # 启动数据库和中间件
    docker-compose up -d postgres mongodb redis elasticsearch minio
    
    echo "⏳ 等待服务启动..."
    sleep 30
    
    # 检查服务状态
    echo "🔍 检查服务状态..."
    docker-compose ps
}

# 初始化数据库
init_database() {
    echo "🗄️  初始化数据库..."
    
    # 等待 PostgreSQL 启动
    echo "等待 PostgreSQL 启动..."
    until docker-compose exec postgres pg_isready -U docmind; do
        sleep 2
    done
    
    # 运行数据库迁移
    echo "运行数据库迁移..."
    # 这里后续会添加具体的迁移命令
    
    echo "✅ 数据库初始化完成"
}

# 创建开发用的目录和文件
setup_development_files() {
    echo "📁 创建开发文件..."
    
    # 创建日志目录
    mkdir -p logs
    
    # 创建上传目录
    mkdir -p uploads
    
    # 创建临时目录
    mkdir -p tmp
    
    # 创建备份目录
    mkdir -p backup
    
    echo "✅ 开发文件创建完成"
}

# 验证安装
verify_installation() {
    echo "🔍 验证安装..."
    
    # 检查数据库连接
    echo "检查 PostgreSQL 连接..."
    if docker-compose exec postgres pg_isready -U docmind; then
        echo "✅ PostgreSQL 连接正常"
    else
        echo "❌ PostgreSQL 连接失败"
    fi
    
    # 检查 MongoDB 连接
    echo "检查 MongoDB 连接..."
    if docker-compose exec mongodb mongosh --eval "db.runCommand('ping')" --quiet; then
        echo "✅ MongoDB 连接正常"
    else
        echo "❌ MongoDB 连接失败"
    fi
    
    # 检查 Redis 连接
    echo "检查 Redis 连接..."
    if docker-compose exec redis redis-cli ping | grep -q PONG; then
        echo "✅ Redis 连接正常"
    else
        echo "❌ Redis 连接失败"
    fi
    
    echo "✅ 验证完成"
}

# 显示下一步操作
show_next_steps() {
    echo ""
    echo "🎉 开发环境设置完成！"
    echo ""
    echo "下一步操作："
    echo "1. 启动开发服务器："
    echo "   npm run dev"
    echo ""
    echo "2. 或者使用 Make 命令："
    echo "   make dev"
    echo ""
    echo "3. 访问应用："
    echo "   前端: http://localhost:3000"
    echo "   API网关: http://localhost:8080"
    echo "   AI服务文档: http://localhost:8001/docs"
    echo ""
    echo "4. 查看服务状态："
    echo "   docker-compose ps"
    echo ""
    echo "5. 查看日志："
    echo "   docker-compose logs -f"
    echo ""
}

# 主函数
main() {
    check_requirements
    install_dependencies
    setup_environment
    start_infrastructure
    init_database
    setup_development_files
    verify_installation
    show_next_steps
}

# 运行主函数
main "$@"
