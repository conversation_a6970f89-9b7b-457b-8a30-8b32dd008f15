import { Router } from 'express';
import { AuthController } from '@/controllers/AuthController';
import { authenticateToken, validateUser, auditLog } from '@/middleware/auth';
import {
  validateBody,
  registerSchema,
  loginSchema,
  updateUserSchema,
  changePasswordSchema,
  passwordResetRequestSchema,
  passwordResetConfirmSchema,
  emailVerificationSchema,
  refreshTokenSchema
} from '@/utils/validation';

const router = Router();

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: 用户注册
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - name
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 minLength: 8
 *               name:
 *                 type: string
 *               phone:
 *                 type: string
 *     responses:
 *       201:
 *         description: 注册成功
 *       400:
 *         description: 验证失败
 *       409:
 *         description: 邮箱已存在
 */
router.post('/register', 
  validateBody(registerSchema),
  auditLog('user.register', 'user'),
  AuthController.register
);

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: 用户登录
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *               remember_me:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: 登录成功
 *       401:
 *         description: 认证失败
 */
router.post('/login',
  validateBody(loginSchema),
  auditLog('user.login', 'user'),
  AuthController.login
);

/**
 * @swagger
 * /api/auth/refresh:
 *   post:
 *     summary: 刷新访问令牌
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refresh_token
 *             properties:
 *               refresh_token:
 *                 type: string
 *     responses:
 *       200:
 *         description: 令牌刷新成功
 *       401:
 *         description: 无效的刷新令牌
 */
router.post('/refresh',
  validateBody(refreshTokenSchema),
  AuthController.refreshToken
);

/**
 * @swagger
 * /api/auth/logout:
 *   post:
 *     summary: 用户登出
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 登出成功
 */
router.post('/logout',
  authenticateToken,
  auditLog('user.logout', 'user'),
  AuthController.logout
);

/**
 * @swagger
 * /api/auth/profile:
 *   get:
 *     summary: 获取当前用户信息
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 用户信息获取成功
 *       401:
 *         description: 未认证
 */
router.get('/profile',
  authenticateToken,
  validateUser,
  AuthController.getProfile
);

/**
 * @swagger
 * /api/auth/profile:
 *   put:
 *     summary: 更新当前用户信息
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               phone:
 *                 type: string
 *               avatar_url:
 *                 type: string
 *                 format: uri
 *     responses:
 *       200:
 *         description: 用户信息更新成功
 *       401:
 *         description: 未认证
 */
router.put('/profile',
  authenticateToken,
  validateUser,
  validateBody(updateUserSchema),
  auditLog('user.update', 'user'),
  AuthController.updateProfile
);

/**
 * @swagger
 * /api/auth/change-password:
 *   post:
 *     summary: 更改密码
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - current_password
 *               - new_password
 *             properties:
 *               current_password:
 *                 type: string
 *               new_password:
 *                 type: string
 *                 minLength: 8
 *     responses:
 *       200:
 *         description: 密码更改成功
 *       401:
 *         description: 当前密码错误
 */
router.post('/change-password',
  authenticateToken,
  validateUser,
  validateBody(changePasswordSchema),
  auditLog('password.change', 'user'),
  AuthController.changePassword
);

/**
 * @swagger
 * /api/auth/forgot-password:
 *   post:
 *     summary: 请求密码重置
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *     responses:
 *       200:
 *         description: 密码重置邮件已发送
 */
router.post('/forgot-password',
  validateBody(passwordResetRequestSchema),
  AuthController.requestPasswordReset
);

/**
 * @swagger
 * /api/auth/reset-password:
 *   post:
 *     summary: 确认密码重置
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *               - new_password
 *             properties:
 *               token:
 *                 type: string
 *               new_password:
 *                 type: string
 *                 minLength: 8
 *     responses:
 *       200:
 *         description: 密码重置成功
 *       400:
 *         description: 无效的重置令牌
 */
router.post('/reset-password',
  validateBody(passwordResetConfirmSchema),
  auditLog('password.reset', 'user'),
  AuthController.confirmPasswordReset
);

/**
 * @swagger
 * /api/auth/send-verification:
 *   post:
 *     summary: 发送邮箱验证
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 验证邮件已发送
 *       401:
 *         description: 未认证
 */
router.post('/send-verification',
  authenticateToken,
  validateUser,
  AuthController.sendEmailVerification
);

/**
 * @swagger
 * /api/auth/verify-email:
 *   post:
 *     summary: 验证邮箱
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *             properties:
 *               token:
 *                 type: string
 *     responses:
 *       200:
 *         description: 邮箱验证成功
 *       400:
 *         description: 无效的验证令牌
 */
router.post('/verify-email',
  validateBody(emailVerificationSchema),
  auditLog('email.verify', 'user'),
  AuthController.verifyEmail
);

export { router as authRoutes };
