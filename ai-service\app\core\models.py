import os
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
import torch
import spacy
from transformers import pipeline, AutoTokenizer, AutoModel
from sentence_transformers import SentenceTransformer

from app.config import settings, get_model_config
from app.core.logging import logger, log_model_operation
from app.core.exceptions import ModelError, ModelNotLoadedError, ModelLoadError

class ModelManager:
    """AI Model Manager for loading and managing various AI models"""
    
    def __init__(self):
        self.models: Dict[str, Any] = {}
        self.model_info: Dict[str, Dict[str, Any]] = {}
        self.loading_locks: Dict[str, asyncio.Lock] = {}
    
    async def load_models(self):
        """Load all required models"""
        logger.info("Starting to load AI models...")
        
        # Load models concurrently
        tasks = []
        
        if settings.ocr_enabled:
            tasks.append(self._load_ocr_models())
        
        if settings.nlp_enabled:
            tasks.append(self._load_nlp_models())
        
        if settings.risk_analysis_enabled:
            tasks.append(self._load_risk_analysis_models())
        
        if settings.contract_analysis_enabled:
            tasks.append(self._load_contract_analysis_models())
        
        # Execute all loading tasks
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Check for any loading errors
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Model loading task {i} failed: {result}")
        
        logger.info(f"Model loading completed. Loaded {len(self.models)} models")
        self._log_model_status()
    
    async def _load_ocr_models(self):
        """Load OCR models"""
        try:
            # OCR models are typically external (Tesseract)
            # We just verify Tesseract is available
            import pytesseract
            
            if settings.tesseract_path:
                pytesseract.pytesseract.tesseract_cmd = settings.tesseract_path
            
            # Test Tesseract
            try:
                version = pytesseract.get_tesseract_version()
                self.models["tesseract"] = pytesseract
                self.model_info["tesseract"] = {
                    "name": "tesseract",
                    "version": str(version),
                    "type": "ocr",
                    "description": "Tesseract OCR Engine",
                    "loaded": True,
                    "last_used": None
                }
                log_model_operation("tesseract", "load", True, version=str(version))
            except Exception as e:
                raise ModelLoadError("tesseract", f"Tesseract not available: {e}")
                
        except Exception as e:
            logger.error(f"Failed to load OCR models: {e}")
            raise
    
    async def _load_nlp_models(self):
        """Load NLP models"""
        try:
            # Load spaCy model
            await self._load_spacy_model()
            
            # Load sentence transformer
            await self._load_sentence_transformer()
            
            # Load other NLP models
            await self._load_transformers_models()
            
        except Exception as e:
            logger.error(f"Failed to load NLP models: {e}")
            raise
    
    async def _load_spacy_model(self):
        """Load spaCy model"""
        model_name = settings.spacy_model
        try:
            # Load in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            nlp = await loop.run_in_executor(None, spacy.load, model_name)
            
            self.models["spacy"] = nlp
            self.model_info["spacy"] = {
                "name": model_name,
                "version": spacy.__version__,
                "type": "nlp",
                "description": "spaCy NLP Model",
                "loaded": True,
                "last_used": None
            }
            log_model_operation("spacy", "load", True, model=model_name)
            
        except OSError as e:
            if "Can't find model" in str(e):
                raise ModelLoadError("spacy", f"Model '{model_name}' not found. Please install it with: python -m spacy download {model_name}")
            else:
                raise ModelLoadError("spacy", str(e))
    
    async def _load_sentence_transformer(self):
        """Load sentence transformer model"""
        model_name = settings.sentence_transformer_model
        try:
            loop = asyncio.get_event_loop()
            model = await loop.run_in_executor(None, SentenceTransformer, model_name)
            
            self.models["sentence_transformer"] = model
            self.model_info["sentence_transformer"] = {
                "name": model_name,
                "version": "latest",
                "type": "embedding",
                "description": "Sentence Transformer Model",
                "loaded": True,
                "last_used": None
            }
            log_model_operation("sentence_transformer", "load", True, model=model_name)
            
        except Exception as e:
            raise ModelLoadError("sentence_transformer", str(e))
    
    async def _load_transformers_models(self):
        """Load Transformers models"""
        try:
            # Load sentiment analysis pipeline
            loop = asyncio.get_event_loop()
            sentiment_pipeline = await loop.run_in_executor(
                None, 
                pipeline, 
                "sentiment-analysis",
                model="cardiffnlp/twitter-roberta-base-sentiment-latest"
            )
            
            self.models["sentiment_analysis"] = sentiment_pipeline
            self.model_info["sentiment_analysis"] = {
                "name": "cardiffnlp/twitter-roberta-base-sentiment-latest",
                "version": "latest",
                "type": "sentiment",
                "description": "Sentiment Analysis Model",
                "loaded": True,
                "last_used": None
            }
            log_model_operation("sentiment_analysis", "load", True)
            
        except Exception as e:
            logger.warning(f"Failed to load sentiment analysis model: {e}")
    
    async def _load_risk_analysis_models(self):
        """Load risk analysis models"""
        try:
            # For now, we'll use rule-based risk analysis
            # In the future, we can load custom trained models
            self.models["risk_analyzer"] = "rule_based"
            self.model_info["risk_analyzer"] = {
                "name": "rule_based_risk_analyzer",
                "version": "1.0.0",
                "type": "risk_analysis",
                "description": "Rule-based Risk Analysis",
                "loaded": True,
                "last_used": None
            }
            log_model_operation("risk_analyzer", "load", True)
            
        except Exception as e:
            logger.error(f"Failed to load risk analysis models: {e}")
            raise
    
    async def _load_contract_analysis_models(self):
        """Load contract analysis models"""
        try:
            # For now, we'll use rule-based contract analysis
            # In the future, we can load custom trained models
            self.models["contract_analyzer"] = "rule_based"
            self.model_info["contract_analyzer"] = {
                "name": "rule_based_contract_analyzer",
                "version": "1.0.0",
                "type": "contract_analysis",
                "description": "Rule-based Contract Analysis",
                "loaded": True,
                "last_used": None
            }
            log_model_operation("contract_analyzer", "load", True)
            
        except Exception as e:
            logger.error(f"Failed to load contract analysis models: {e}")
            raise
    
    def get_model(self, model_name: str):
        """Get loaded model"""
        if model_name not in self.models:
            raise ModelNotLoadedError(model_name)
        
        # Update last used time
        if model_name in self.model_info:
            self.model_info[model_name]["last_used"] = datetime.utcnow()
        
        return self.models[model_name]
    
    def is_model_loaded(self, model_name: str) -> bool:
        """Check if model is loaded"""
        return model_name in self.models
    
    def get_model_info(self, model_name: str = None) -> Dict[str, Any]:
        """Get model information"""
        if model_name:
            if model_name not in self.model_info:
                raise ModelNotLoadedError(model_name)
            return self.model_info[model_name]
        return self.model_info
    
    def get_loaded_models(self) -> List[str]:
        """Get list of loaded model names"""
        return list(self.models.keys())
    
    async def unload_model(self, model_name: str):
        """Unload a specific model"""
        if model_name in self.models:
            del self.models[model_name]
            if model_name in self.model_info:
                self.model_info[model_name]["loaded"] = False
            log_model_operation(model_name, "unload", True)
            logger.info(f"Model {model_name} unloaded")
    
    async def reload_model(self, model_name: str):
        """Reload a specific model"""
        await self.unload_model(model_name)
        
        # Reload based on model type
        if model_name == "spacy":
            await self._load_spacy_model()
        elif model_name == "sentence_transformer":
            await self._load_sentence_transformer()
        elif model_name == "sentiment_analysis":
            await self._load_transformers_models()
        elif model_name == "tesseract":
            await self._load_ocr_models()
        elif model_name == "risk_analyzer":
            await self._load_risk_analysis_models()
        elif model_name == "contract_analyzer":
            await self._load_contract_analysis_models()
        else:
            raise ModelError(f"Unknown model: {model_name}")
    
    def _log_model_status(self):
        """Log current model status"""
        loaded_models = []
        failed_models = []
        
        for name, info in self.model_info.items():
            if info["loaded"]:
                loaded_models.append(name)
            else:
                failed_models.append(name)
        
        logger.info(f"Loaded models: {loaded_models}")
        if failed_models:
            logger.warning(f"Failed to load models: {failed_models}")
    
    def get_memory_usage(self) -> Dict[str, float]:
        """Get memory usage of loaded models"""
        memory_usage = {}
        
        for model_name, model in self.models.items():
            try:
                if hasattr(model, 'get_memory_footprint'):
                    memory_usage[model_name] = model.get_memory_footprint()
                elif torch.cuda.is_available() and hasattr(model, 'cuda'):
                    # For PyTorch models
                    memory_usage[model_name] = torch.cuda.memory_allocated() / 1024**2  # MB
                else:
                    memory_usage[model_name] = 0.0
            except Exception:
                memory_usage[model_name] = 0.0
        
        return memory_usage

# Global model manager instance
model_manager = ModelManager()

# Convenience functions
async def load_models():
    """Load all models"""
    await model_manager.load_models()

def get_model(model_name: str):
    """Get a loaded model"""
    return model_manager.get_model(model_name)

def is_model_loaded(model_name: str) -> bool:
    """Check if model is loaded"""
    return model_manager.is_model_loaded(model_name)

def get_model_info(model_name: str = None):
    """Get model information"""
    return model_manager.get_model_info(model_name)

def get_loaded_models() -> List[str]:
    """Get list of loaded models"""
    return model_manager.get_loaded_models()
