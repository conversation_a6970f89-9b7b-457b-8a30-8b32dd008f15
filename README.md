# DocMind - 合同智能审阅 SaaS 服务

## 项目简介

DocMind 是一个基于人工智能的合同智能审阅SaaS平台，为律师事务所、企业法务部门和合规团队提供高效、准确的合同分析和风险识别服务。

## 核心功能

- 🤖 **智能合同解析**: OCR识别、结构化解析、多格式支持
- ⚖️ **法律条款理解**: 语义分析、条款分类、关联分析
- 🚨 **风险识别评估**: 全面扫描、风险分级、案例参考
- 📝 **自动摘要生成**: 合同摘要、风险摘要、变更摘要
- 💡 **修订建议系统**: 智能建议、模板推荐、影响分析
- 👥 **多人协作审阅**: 实时协作、版本控制、审批流程

## 技术架构

```
Frontend (React)
    ↓
API Gateway (Nginx)
    ↓
Microservices (Node.js/Python)
    ↓
Databases (PostgreSQL/MongoDB/Redis)
    ↓
AI Services (PyTorch/Transformers)
```

## 项目结构

```
DocMind/
├── docs/                      # 项目文档
│   ├── product/               # 产品文档
│   ├── technical/             # 技术文档
│   └── development/           # 开发文档
├── frontend/                  # React前端应用
├── backend/                   # 后端微服务
│   ├── user-service/         # 用户服务
│   ├── document-service/     # 文档服务
│   ├── collaboration-service/ # 协作服务
│   ├── notification-service/ # 通知服务
│   ├── search-service/       # 搜索服务
│   └── shared/               # 共享模块
├── ai-service/               # AI分析服务
│   ├── ocr-service/          # OCR识别
│   ├── nlp-service/          # NLP处理
│   ├── risk-analysis/        # 风险分析
│   ├── recommendation/       # 建议生成
│   ├── models/               # AI模型
│   └── shared/               # 共享模块
├── infrastructure/           # 基础设施
│   ├── docker/               # Docker配置
│   ├── kubernetes/           # K8s配置
│   ├── terraform/            # 基础设施代码
│   └── monitoring/           # 监控配置
├── scripts/                  # 脚本文件
├── config/                   # 配置文件
├── docker-compose.yml        # Docker Compose配置
├── package.json              # 项目配置
└── README.md                 # 项目说明
```

## 快速开始

### 环境要求

- Node.js 18+
- Python 3.9+
- Docker & Docker Compose
- Git

### 安装依赖

```bash
# 安装项目依赖
npm install

# 安装各服务依赖
npm run setup
```

### 启动开发环境

```bash
# 启动数据库和基础服务
docker-compose up -d postgres mongodb redis elasticsearch minio

# 启动所有服务
npm run dev
```

### 使用Docker启动完整环境

```bash
# 构建并启动所有服务
docker-compose up --build

# 后台运行
docker-compose up -d --build
```

## 开发指南

### 前端开发

```bash
cd frontend
npm install
npm run dev
```

访问: http://localhost:3000

### 后端开发

```bash
# 用户服务
cd backend/user-service
npm install
npm run dev

# 文档服务
cd backend/document-service
npm install
npm run dev
```

### AI服务开发

```bash
cd ai-service
pip install -r requirements.txt
python -m uvicorn main:app --reload --port 8001
```

访问API文档: http://localhost:8001/docs

### 数据库管理

```bash
# PostgreSQL
psql -h localhost -p 5432 -U docmind -d docmind

# MongoDB
mongosh ****************************************************

# Redis
redis-cli -h localhost -p 6379
```

## 测试

```bash
# 运行所有测试
npm test

# 运行特定服务测试
npm run test:frontend
npm run test:backend
npm run test:ai
```

## 部署

### 开发环境部署

```bash
# 使用Docker Compose
docker-compose -f docker-compose.yml up -d
```

### 生产环境部署

```bash
# 使用Kubernetes
kubectl apply -f infrastructure/kubernetes/
```

## API文档

- 用户服务: http://localhost:3001/api/docs
- 文档服务: http://localhost:3002/api/docs
- 协作服务: http://localhost:3003/api/docs
- AI服务: http://localhost:8001/docs

## 监控和日志

- Grafana: http://localhost:3000
- Elasticsearch: http://localhost:9200
- Kibana: http://localhost:5601

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 代码规范

- 使用 ESLint 和 Prettier 进行代码格式化
- 遵循 Git 提交信息规范
- 编写单元测试和集成测试
- 更新相关文档

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系我们

- 项目主页: https://github.com/docmind/docmind
- 问题反馈: https://github.com/docmind/docmind/issues
- 邮箱: <EMAIL>

## 更新日志

查看 [CHANGELOG.md](CHANGELOG.md) 了解版本更新信息。
