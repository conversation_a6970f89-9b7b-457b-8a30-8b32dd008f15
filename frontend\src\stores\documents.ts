import { create } from 'zustand';
import { 
  Document, 
  DocumentSearchParams, 
  DocumentUploadForm,
  DocumentUpdateForm,
  PaginatedResponse,
  UploadProgress 
} from '@/types';
import DocumentService from '@/services/documents';

interface DocumentState {
  // 文档列表状态
  documents: Document[];
  currentDocument: Document | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
  
  // 搜索和过滤状态
  searchParams: DocumentSearchParams;
  
  // 上传状态
  uploadProgress: UploadProgress[];
  
  // 加载状态
  isLoading: boolean;
  isUploading: boolean;
  error: string | null;
  
  // 选择状态
  selectedDocuments: string[];
  
  // 操作
  fetchDocuments: (params?: DocumentSearchParams) => Promise<void>;
  searchDocuments: (params: DocumentSearchParams) => Promise<void>;
  fetchDocumentById: (id: string) => Promise<void>;
  uploadDocument: (data: DocumentUploadForm) => Promise<Document>;
  updateDocument: (id: string, data: DocumentUpdateForm) => Promise<void>;
  deleteDocument: (id: string) => Promise<void>;
  bulkDeleteDocuments: (ids: string[]) => Promise<void>;
  
  // 搜索参数管理
  setSearchParams: (params: Partial<DocumentSearchParams>) => void;
  clearSearchParams: () => void;
  
  // 选择管理
  selectDocument: (id: string) => void;
  selectAllDocuments: () => void;
  deselectDocument: (id: string) => void;
  clearSelection: () => void;
  
  // 状态管理
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // 上传进度管理
  addUploadProgress: (progress: UploadProgress) => void;
  updateUploadProgress: (fileId: string, progress: Partial<UploadProgress>) => void;
  removeUploadProgress: (fileId: string) => void;
  clearUploadProgress: () => void;
}

export const useDocumentStore = create<DocumentState>((set, get) => ({
  // 初始状态
  documents: [],
  currentDocument: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    pages: 0,
    has_next: false,
    has_prev: false,
  },
  searchParams: {
    page: 1,
    limit: 20,
    sort: 'created_at',
    order: 'desc',
  },
  uploadProgress: [],
  isLoading: false,
  isUploading: false,
  error: null,
  selectedDocuments: [],

  // 获取文档列表
  fetchDocuments: async (params?: DocumentSearchParams) => {
    set({ isLoading: true, error: null });
    
    try {
      const searchParams = { ...get().searchParams, ...params };
      const response = await DocumentService.getDocuments(searchParams);
      
      set({
        documents: response.data,
        pagination: response.pagination,
        searchParams,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '获取文档列表失败',
      });
    }
  },

  // 搜索文档
  searchDocuments: async (params: DocumentSearchParams) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await DocumentService.searchDocuments(params);
      
      set({
        documents: response.data,
        pagination: response.pagination,
        searchParams: params,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '搜索文档失败',
      });
    }
  },

  // 获取单个文档
  fetchDocumentById: async (id: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const document = await DocumentService.getDocumentById(id);
      set({
        currentDocument: document,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '获取文档详情失败',
      });
    }
  },

  // 上传文档
  uploadDocument: async (data: DocumentUploadForm) => {
    set({ isUploading: true, error: null });
    
    // 添加上传进度
    const progressId = `${data.file.name}-${Date.now()}`;
    get().addUploadProgress({
      file: data.file,
      progress: 0,
      status: 'pending',
    });
    
    try {
      const document = await DocumentService.uploadDocument(data, (progress) => {
        get().updateUploadProgress(progressId, { progress, status: 'uploading' });
      });
      
      // 更新上传进度为完成
      get().updateUploadProgress(progressId, { 
        progress: 100, 
        status: 'completed' 
      });
      
      // 刷新文档列表
      await get().fetchDocuments();
      
      set({ isUploading: false });
      
      // 延迟移除上传进度
      setTimeout(() => {
        get().removeUploadProgress(progressId);
      }, 2000);
      
      return document;
    } catch (error) {
      get().updateUploadProgress(progressId, { 
        status: 'error',
        error: error instanceof Error ? error.message : '上传失败'
      });
      
      set({
        isUploading: false,
        error: error instanceof Error ? error.message : '文档上传失败',
      });
      
      throw error;
    }
  },

  // 更新文档
  updateDocument: async (id: string, data: DocumentUpdateForm) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedDocument = await DocumentService.updateDocument(id, data);
      
      // 更新文档列表中的文档
      set((state) => ({
        documents: state.documents.map(doc => 
          doc.id === id ? updatedDocument : doc
        ),
        currentDocument: state.currentDocument?.id === id ? updatedDocument : state.currentDocument,
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '更新文档失败',
      });
      throw error;
    }
  },

  // 删除文档
  deleteDocument: async (id: string) => {
    set({ isLoading: true, error: null });
    
    try {
      await DocumentService.deleteDocument(id);
      
      // 从文档列表中移除
      set((state) => ({
        documents: state.documents.filter(doc => doc.id !== id),
        selectedDocuments: state.selectedDocuments.filter(docId => docId !== id),
        currentDocument: state.currentDocument?.id === id ? null : state.currentDocument,
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '删除文档失败',
      });
      throw error;
    }
  },

  // 批量删除文档
  bulkDeleteDocuments: async (ids: string[]) => {
    set({ isLoading: true, error: null });
    
    try {
      await DocumentService.bulkDeleteDocuments(ids);
      
      // 从文档列表中移除
      set((state) => ({
        documents: state.documents.filter(doc => !ids.includes(doc.id)),
        selectedDocuments: state.selectedDocuments.filter(docId => !ids.includes(docId)),
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '批量删除文档失败',
      });
      throw error;
    }
  },

  // 设置搜索参数
  setSearchParams: (params: Partial<DocumentSearchParams>) => {
    set((state) => ({
      searchParams: { ...state.searchParams, ...params },
    }));
  },

  // 清除搜索参数
  clearSearchParams: () => {
    set({
      searchParams: {
        page: 1,
        limit: 20,
        sort: 'created_at',
        order: 'desc',
      },
    });
  },

  // 选择文档
  selectDocument: (id: string) => {
    set((state) => ({
      selectedDocuments: state.selectedDocuments.includes(id)
        ? state.selectedDocuments
        : [...state.selectedDocuments, id],
    }));
  },

  // 选择所有文档
  selectAllDocuments: () => {
    set((state) => ({
      selectedDocuments: state.documents.map(doc => doc.id),
    }));
  },

  // 取消选择文档
  deselectDocument: (id: string) => {
    set((state) => ({
      selectedDocuments: state.selectedDocuments.filter(docId => docId !== id),
    }));
  },

  // 清除选择
  clearSelection: () => {
    set({ selectedDocuments: [] });
  },

  // 设置加载状态
  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  // 设置错误
  setError: (error: string | null) => {
    set({ error });
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },

  // 添加上传进度
  addUploadProgress: (progress: UploadProgress) => {
    set((state) => ({
      uploadProgress: [...state.uploadProgress, progress],
    }));
  },

  // 更新上传进度
  updateUploadProgress: (fileId: string, progress: Partial<UploadProgress>) => {
    set((state) => ({
      uploadProgress: state.uploadProgress.map(item =>
        item.file.name === fileId ? { ...item, ...progress } : item
      ),
    }));
  },

  // 移除上传进度
  removeUploadProgress: (fileId: string) => {
    set((state) => ({
      uploadProgress: state.uploadProgress.filter(item => item.file.name !== fileId),
    }));
  },

  // 清除所有上传进度
  clearUploadProgress: () => {
    set({ uploadProgress: [] });
  },
}));
