import fs from 'fs-extra';
import path from 'path';
import crypto from 'crypto';
import mime from 'mime-types';
import { FileTypeResult, fileTypeFromBuffer } from 'file-type';
import sharp from 'sharp';
import { config } from '@/config';
import { logger } from '@/utils/logger';

export class FileUtils {
  /**
   * 生成唯一文件名
   */
  static generateUniqueFileName(originalName: string): string {
    const ext = path.extname(originalName);
    const name = path.basename(originalName, ext);
    const timestamp = Date.now();
    const random = crypto.randomBytes(8).toString('hex');
    return `${name}_${timestamp}_${random}${ext}`;
  }

  /**
   * 生成文件路径
   */
  static generateFilePath(organizationId: string, fileName: string): string {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${organizationId}/${year}/${month}/${day}/${fileName}`;
  }

  /**
   * 计算文件哈希
   */
  static async calculateFileHash(filePath: string, algorithm: string = 'sha256'): Promise<string> {
    return new Promise((resolve, reject) => {
      const hash = crypto.createHash(algorithm);
      const stream = fs.createReadStream(filePath);
      
      stream.on('data', (data) => hash.update(data));
      stream.on('end', () => resolve(hash.digest('hex')));
      stream.on('error', reject);
    });
  }

  /**
   * 计算Buffer哈希
   */
  static calculateBufferHash(buffer: Buffer, algorithm: string = 'sha256'): string {
    return crypto.createHash(algorithm).update(buffer).digest('hex');
  }

  /**
   * 验证文件类型
   */
  static async validateFileType(filePath: string): Promise<{
    isValid: boolean;
    detectedType?: FileTypeResult;
    mimeType?: string;
  }> {
    try {
      const buffer = await fs.readFile(filePath);
      const detectedType = await fileTypeFromBuffer(buffer);
      
      if (!detectedType) {
        return { isValid: false };
      }

      const isAllowed = config.upload.allowedMimeTypes.includes(detectedType.mime);
      
      return {
        isValid: isAllowed,
        detectedType,
        mimeType: detectedType.mime
      };
    } catch (error) {
      logger.error('Failed to validate file type:', error);
      return { isValid: false };
    }
  }

  /**
   * 验证文件大小
   */
  static async validateFileSize(filePath: string): Promise<{
    isValid: boolean;
    size: number;
  }> {
    try {
      const stats = await fs.stat(filePath);
      const isValid = stats.size <= config.upload.maxFileSize;
      
      return {
        isValid,
        size: stats.size
      };
    } catch (error) {
      logger.error('Failed to validate file size:', error);
      return { isValid: false, size: 0 };
    }
  }

  /**
   * 获取文件信息
   */
  static async getFileInfo(filePath: string): Promise<{
    size: number;
    mimeType: string;
    extension: string;
    hash: string;
    createdAt: Date;
    modifiedAt: Date;
  }> {
    try {
      const stats = await fs.stat(filePath);
      const hash = await this.calculateFileHash(filePath);
      const extension = path.extname(filePath).toLowerCase();
      const mimeType = mime.lookup(filePath) || 'application/octet-stream';

      return {
        size: stats.size,
        mimeType,
        extension,
        hash,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime
      };
    } catch (error) {
      logger.error('Failed to get file info:', error);
      throw error;
    }
  }

  /**
   * 创建缩略图
   */
  static async createThumbnail(
    inputPath: string,
    outputPath: string,
    options: {
      width?: number;
      height?: number;
      quality?: number;
    } = {}
  ): Promise<void> {
    try {
      const {
        width = config.processing.thumbnailSize.width,
        height = config.processing.thumbnailSize.height,
        quality = 80
      } = options;

      await fs.ensureDir(path.dirname(outputPath));

      await sharp(inputPath)
        .resize(width, height, {
          fit: 'inside',
          withoutEnlargement: true
        })
        .jpeg({ quality })
        .toFile(outputPath);

      logger.debug('Thumbnail created:', { input: inputPath, output: outputPath });
    } catch (error) {
      logger.error('Failed to create thumbnail:', { input: inputPath, output: outputPath, error });
      throw error;
    }
  }

  /**
   * 清理临时文件
   */
  static async cleanupTempFiles(maxAge: number = config.cleanup.tempFileMaxAge): Promise<void> {
    try {
      const tempDir = config.upload.tempPath;
      if (!await fs.pathExists(tempDir)) {
        return;
      }

      const files = await fs.readdir(tempDir);
      const now = Date.now();
      let cleanedCount = 0;

      for (const file of files) {
        const filePath = path.join(tempDir, file);
        const stats = await fs.stat(filePath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          await fs.remove(filePath);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        logger.info(`Cleaned up ${cleanedCount} temporary files`);
      }
    } catch (error) {
      logger.error('Failed to cleanup temporary files:', error);
    }
  }

  /**
   * 格式化文件大小
   */
  static formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * 获取文件扩展名
   */
  static getFileExtension(fileName: string): string {
    return path.extname(fileName).toLowerCase().substring(1);
  }

  /**
   * 验证文件名
   */
  static validateFileName(fileName: string): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // 检查文件名长度
    if (fileName.length > 255) {
      errors.push('File name is too long (max 255 characters)');
    }

    // 检查非法字符
    const illegalChars = /[<>:"/\\|?*\x00-\x1f]/;
    if (illegalChars.test(fileName)) {
      errors.push('File name contains illegal characters');
    }

    // 检查保留名称
    const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
    const nameWithoutExt = path.basename(fileName, path.extname(fileName)).toUpperCase();
    if (reservedNames.includes(nameWithoutExt)) {
      errors.push('File name is reserved');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 安全地删除文件
   */
  static async safeDeleteFile(filePath: string): Promise<void> {
    try {
      if (await fs.pathExists(filePath)) {
        await fs.remove(filePath);
        logger.debug('File deleted safely:', { path: filePath });
      }
    } catch (error) {
      logger.error('Failed to delete file safely:', { path: filePath, error });
      // 不抛出错误，因为这是清理操作
    }
  }

  /**
   * 复制文件
   */
  static async copyFile(sourcePath: string, destPath: string): Promise<void> {
    try {
      await fs.ensureDir(path.dirname(destPath));
      await fs.copy(sourcePath, destPath);
      logger.debug('File copied:', { source: sourcePath, dest: destPath });
    } catch (error) {
      logger.error('Failed to copy file:', { source: sourcePath, dest: destPath, error });
      throw error;
    }
  }

  /**
   * 移动文件
   */
  static async moveFile(sourcePath: string, destPath: string): Promise<void> {
    try {
      await fs.ensureDir(path.dirname(destPath));
      await fs.move(sourcePath, destPath);
      logger.debug('File moved:', { source: sourcePath, dest: destPath });
    } catch (error) {
      logger.error('Failed to move file:', { source: sourcePath, dest: destPath, error });
      throw error;
    }
  }

  /**
   * 检查磁盘空间
   */
  static async checkDiskSpace(path: string): Promise<{
    free: number;
    total: number;
    used: number;
    percentage: number;
  }> {
    try {
      const stats = await fs.stat(path);
      // 这里简化处理，实际应用中可能需要使用专门的库来获取磁盘空间信息
      return {
        free: 0,
        total: 0,
        used: 0,
        percentage: 0
      };
    } catch (error) {
      logger.error('Failed to check disk space:', error);
      throw error;
    }
  }
}
