import asyncio
import time
import re
from typing import List, Dict, Any, Optional
import jieba
import jieba.analyse
from collections import Counter
import numpy as np

from app.config import settings
from app.core.models import get_model
from app.core.logging import logger, log_execution_time
from app.core.exceptions import NLPError, ModelNotLoadedError
from app.models.schemas import (
    NLPRequest, NLPResult, Entity, Keyword, SentimentResult
)

class NLPService:
    """NLP Service for text analysis and processing"""
    
    def __init__(self):
        self.stop_words = self._load_stop_words()
    
    async def process_text(self, request: NLPRequest) -> NLPResult:
        """Process text for NLP analysis"""
        start_time = time.time()
        
        try:
            # Detect language if auto
            language = request.language
            if language == "auto":
                language = self._detect_language(request.text)
            
            # Initialize result components
            entities = []
            keywords = []
            summary = None
            sentiment = None
            
            # Extract entities if requested
            if request.extract_entities:
                entities = await self._extract_entities(request.text, language)
            
            # Extract keywords if requested
            if request.extract_keywords:
                keywords = await self._extract_keywords(request.text, language)
            
            # Generate summary if requested
            if request.generate_summary:
                summary = await self._generate_summary(request.text, language)
            
            # Analyze sentiment if requested
            if request.sentiment_analysis:
                sentiment = await self._analyze_sentiment(request.text, language)
            
            # Calculate text statistics
            word_count = self._count_words(request.text, language)
            sentence_count = self._count_sentences(request.text)
            
            processing_time = time.time() - start_time
            
            result = NLPResult(
                document_id=request.document_id,
                language=language,
                entities=entities,
                keywords=keywords,
                summary=summary,
                sentiment=sentiment,
                word_count=word_count,
                sentence_count=sentence_count,
                processing_time=processing_time,
                metadata={
                    "original_language": request.language,
                    "text_length": len(request.text)
                }
            )
            
            logger.info(f"NLP processing completed for document {request.document_id}",
                       extra={
                           "document_id": request.document_id,
                           "language": language,
                           "word_count": word_count,
                           "entities_count": len(entities),
                           "keywords_count": len(keywords),
                           "processing_time": processing_time
                       })
            
            return result
            
        except Exception as e:
            logger.error(f"NLP processing failed for document {request.document_id}: {e}")
            raise NLPError(str(e), metadata={"document_id": request.document_id})
    
    async def _extract_entities(self, text: str, language: str) -> List[Entity]:
        """Extract named entities from text"""
        try:
            entities = []
            
            if language == "zh":
                # Use spaCy for Chinese NER
                nlp = get_model("spacy")
                
                # Process text in thread pool
                loop = asyncio.get_event_loop()
                doc = await loop.run_in_executor(None, nlp, text)
                
                for ent in doc.ents:
                    entity = Entity(
                        text=ent.text,
                        label=ent.label_,
                        start=ent.start_char,
                        end=ent.end_char,
                        confidence=0.8  # spaCy doesn't provide confidence scores
                    )
                    entities.append(entity)
            
            else:
                # Use regex patterns for basic entity extraction
                entities.extend(self._extract_entities_regex(text))
            
            return entities
            
        except Exception as e:
            logger.warning(f"Entity extraction failed: {e}")
            return []
    
    def _extract_entities_regex(self, text: str) -> List[Entity]:
        """Extract entities using regex patterns"""
        entities = []
        
        # Email pattern
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        for match in re.finditer(email_pattern, text):
            entities.append(Entity(
                text=match.group(),
                label="EMAIL",
                start=match.start(),
                end=match.end(),
                confidence=0.9
            ))
        
        # Phone pattern (Chinese format)
        phone_pattern = r'1[3-9]\d{9}'
        for match in re.finditer(phone_pattern, text):
            entities.append(Entity(
                text=match.group(),
                label="PHONE",
                start=match.start(),
                end=match.end(),
                confidence=0.8
            ))
        
        # Date pattern
        date_pattern = r'\d{4}[年\-/]\d{1,2}[月\-/]\d{1,2}[日]?'
        for match in re.finditer(date_pattern, text):
            entities.append(Entity(
                text=match.group(),
                label="DATE",
                start=match.start(),
                end=match.end(),
                confidence=0.7
            ))
        
        # Money pattern
        money_pattern = r'[￥$]\s*\d+(?:,\d{3})*(?:\.\d{2})?|(?:\d+(?:,\d{3})*(?:\.\d{2})?)\s*[元美元]'
        for match in re.finditer(money_pattern, text):
            entities.append(Entity(
                text=match.group(),
                label="MONEY",
                start=match.start(),
                end=match.end(),
                confidence=0.8
            ))
        
        return entities
    
    async def _extract_keywords(self, text: str, language: str) -> List[Keyword]:
        """Extract keywords from text"""
        try:
            keywords = []
            
            if language == "zh":
                # Use jieba for Chinese keyword extraction
                loop = asyncio.get_event_loop()
                keyword_list = await loop.run_in_executor(
                    None, 
                    jieba.analyse.extract_tags, 
                    text, 
                    topK=20, 
                    withWeight=True
                )
                
                for word, weight in keyword_list:
                    # Count frequency
                    frequency = text.count(word)
                    keywords.append(Keyword(
                        text=word,
                        score=weight,
                        frequency=frequency
                    ))
            
            else:
                # Simple keyword extraction for English
                keywords = self._extract_keywords_simple(text)
            
            return sorted(keywords, key=lambda x: x.score, reverse=True)
            
        except Exception as e:
            logger.warning(f"Keyword extraction failed: {e}")
            return []
    
    def _extract_keywords_simple(self, text: str) -> List[Keyword]:
        """Simple keyword extraction for English text"""
        # Clean and tokenize text
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        
        # Remove stop words
        words = [word for word in words if word not in self.stop_words]
        
        # Count word frequencies
        word_counts = Counter(words)
        
        # Calculate TF scores
        total_words = len(words)
        keywords = []
        
        for word, count in word_counts.most_common(20):
            tf_score = count / total_words
            keywords.append(Keyword(
                text=word,
                score=tf_score,
                frequency=count
            ))
        
        return keywords
    
    async def _generate_summary(self, text: str, language: str) -> Optional[str]:
        """Generate text summary"""
        try:
            # Simple extractive summarization
            sentences = self._split_sentences(text)
            
            if len(sentences) <= 3:
                return text
            
            # Score sentences based on keyword frequency
            keywords = await self._extract_keywords(text, language)
            keyword_texts = [kw.text for kw in keywords[:10]]
            
            sentence_scores = []
            for sentence in sentences:
                score = sum(1 for keyword in keyword_texts if keyword in sentence.lower())
                sentence_scores.append((sentence, score))
            
            # Select top sentences
            sentence_scores.sort(key=lambda x: x[1], reverse=True)
            top_sentences = [sent for sent, score in sentence_scores[:3]]
            
            # Maintain original order
            summary_sentences = []
            for sentence in sentences:
                if sentence in top_sentences:
                    summary_sentences.append(sentence)
            
            return " ".join(summary_sentences)
            
        except Exception as e:
            logger.warning(f"Summary generation failed: {e}")
            return None
    
    async def _analyze_sentiment(self, text: str, language: str) -> Optional[SentimentResult]:
        """Analyze text sentiment"""
        try:
            if language == "en":
                # Use transformers sentiment analysis for English
                sentiment_pipeline = get_model("sentiment_analysis")
                
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(None, sentiment_pipeline, text[:512])
                
                if result and len(result) > 0:
                    sentiment_data = result[0]
                    return SentimentResult(
                        label=sentiment_data['label'].lower(),
                        score=sentiment_data['score'],
                        confidence=sentiment_data['score']
                    )
            
            else:
                # Simple rule-based sentiment for Chinese
                return self._analyze_sentiment_rule_based(text)
            
        except Exception as e:
            logger.warning(f"Sentiment analysis failed: {e}")
            return None
    
    def _analyze_sentiment_rule_based(self, text: str) -> SentimentResult:
        """Rule-based sentiment analysis"""
        positive_words = ["好", "优秀", "满意", "成功", "高兴", "喜欢", "赞", "棒", "完美"]
        negative_words = ["坏", "差", "失败", "生气", "讨厌", "糟糕", "问题", "错误", "不满"]
        
        positive_count = sum(1 for word in positive_words if word in text)
        negative_count = sum(1 for word in negative_words if word in text)
        
        total_count = positive_count + negative_count
        
        if total_count == 0:
            return SentimentResult(label="neutral", score=0.5, confidence=0.5)
        
        positive_ratio = positive_count / total_count
        
        if positive_ratio > 0.6:
            return SentimentResult(label="positive", score=positive_ratio, confidence=0.7)
        elif positive_ratio < 0.4:
            return SentimentResult(label="negative", score=1 - positive_ratio, confidence=0.7)
        else:
            return SentimentResult(label="neutral", score=0.5, confidence=0.6)
    
    def _detect_language(self, text: str) -> str:
        """Detect text language"""
        # Simple heuristic: check for Chinese characters
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        total_chars = len([char for char in text if char.isalpha()])
        
        if total_chars == 0:
            return "en"
        
        chinese_ratio = chinese_chars / total_chars
        return "zh" if chinese_ratio > 0.3 else "en"
    
    def _count_words(self, text: str, language: str) -> int:
        """Count words in text"""
        if language == "zh":
            # Use jieba for Chinese word segmentation
            words = jieba.lcut(text)
            return len([word for word in words if word.strip() and not word.isspace()])
        else:
            # Simple word count for English
            words = re.findall(r'\b\w+\b', text)
            return len(words)
    
    def _count_sentences(self, text: str) -> int:
        """Count sentences in text"""
        sentences = self._split_sentences(text)
        return len(sentences)
    
    def _split_sentences(self, text: str) -> List[str]:
        """Split text into sentences"""
        # Split by common sentence endings
        sentences = re.split(r'[.!?。！？]+', text)
        return [sent.strip() for sent in sentences if sent.strip()]
    
    def _load_stop_words(self) -> set:
        """Load stop words"""
        # Basic English stop words
        english_stop_words = {
            'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
            'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
            'to', 'was', 'will', 'with', 'would', 'you', 'your', 'this', 'they',
            'we', 'have', 'had', 'what', 'when', 'where', 'who', 'which', 'why',
            'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other',
            'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so',
            'than', 'too', 'very', 'can', 'could', 'should', 'would'
        }
        
        return english_stop_words

# Global NLP service instance
nlp_service = NLPService()
