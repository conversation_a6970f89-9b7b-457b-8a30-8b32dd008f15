import winston from 'winston';
import { config } from '@/config';

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    return JSON.stringify({
      timestamp,
      level,
      message,
      service: 'document-service',
      ...meta
    });
  })
);

// 开发环境格式
const devFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${level}]: ${message} ${metaStr}`;
  })
);

// 创建传输器
const transports: winston.transport[] = [
  new winston.transports.Console({
    format: config.app.env === 'development' ? devFormat : logFormat
  })
];

// 生产环境添加文件日志
if (config.app.env === 'production') {
  transports.push(
    new winston.transports.File({
      filename: config.log.file,
      format: logFormat,
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5
    })
  );
}

// 创建logger实例
export const logger = winston.createLogger({
  level: config.log.level,
  format: logFormat,
  transports,
  exitOnError: false
});

// 在非生产环境下，将错误日志也输出到控制台
if (config.app.env !== 'production') {
  logger.exceptions.handle(
    new winston.transports.Console({
      format: devFormat
    })
  );
}

export default logger;
