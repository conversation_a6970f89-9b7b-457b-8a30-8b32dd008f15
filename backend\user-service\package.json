{"name": "docmind-user-service", "version": "1.0.0", "description": "DocMind User Service - 用户认证和权限管理服务", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "migrate": "npm run build && node dist/database/migrate.js", "seed": "npm run build && node dist/database/seed.js", "docker:build": "docker build -t docmind-user-service .", "docker:run": "docker run -p 3001:3000 docmind-user-service"}, "keywords": ["user", "authentication", "authorization", "jwt", "rbac"], "author": "DocMind Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "pg": "^8.11.3", "redis": "^4.6.10", "nodemailer": "^6.9.7", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "winston": "^3.11.0", "express-winston": "^4.2.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "uuid": "^9.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.8.10", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/pg": "^8.10.7", "@types/nodemailer": "^6.4.14", "@types/speakeasy": "^2.0.10", "@types/qrcode": "^1.5.5", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.2.2", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.53.0", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "prettier": "^3.0.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}