import { Router } from 'express';
import { HealthController } from '@/controllers/HealthController';

const router = Router();

/**
 * @swagger
 * /health:
 *   get:
 *     summary: 基础健康检查
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: 服务健康
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: healthy
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 service:
 *                   type: string
 *                 version:
 *                   type: string
 *                 environment:
 *                   type: string
 *                 uptime:
 *                   type: number
 */
router.get('/', HealthController.healthCheck);

/**
 * @swagger
 * /health/detailed:
 *   get:
 *     summary: 详细健康检查
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: 服务健康
 *       503:
 *         description: 服务不健康
 */
router.get('/detailed', HealthController.detailedHealthCheck);

/**
 * @swagger
 * /health/ready:
 *   get:
 *     summary: 就绪检查
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: 服务就绪
 *       503:
 *         description: 服务未就绪
 */
router.get('/ready', HealthController.readinessCheck);

/**
 * @swagger
 * /health/live:
 *   get:
 *     summary: 存活检查
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: 服务存活
 */
router.get('/live', HealthController.livenessCheck);

/**
 * @swagger
 * /health/info:
 *   get:
 *     summary: 系统信息
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: 系统信息获取成功
 */
router.get('/info', HealthController.getSystemInfo);

export { router as healthRoutes };
