import { Pool, PoolClient } from 'pg';
import { MongoClient, Db } from 'mongodb';
import { createClient, RedisClientType } from 'redis';
import { config } from '@/config';
import { logger } from '@/utils/logger';

// PostgreSQL连接
let pgPool: Pool;
let mongoClient: MongoClient;
let mongoDB: Db;
let redisClient: RedisClientType;

export async function connectPostgreSQL(): Promise<Pool> {
  try {
    pgPool = new Pool({
      connectionString: config.database.postgres.url,
      min: config.database.postgres.pool.min,
      max: config.database.postgres.pool.max,
      idleTimeoutMillis: config.database.postgres.pool.idle,
      connectionTimeoutMillis: 5000,
      ssl: config.app.env === 'production' ? { rejectUnauthorized: false } : false
    });

    // 测试连接
    const client = await pgPool.connect();
    await client.query('SELECT NOW()');
    client.release();

    logger.info('PostgreSQL connection established successfully');
    return pgPool;
  } catch (error) {
    logger.error('Failed to connect to PostgreSQL:', error);
    throw error;
  }
}

export async function connectMongoDB(): Promise<Db> {
  try {
    mongoClient = new MongoClient(config.database.mongodb.url, config.database.mongodb.options);
    await mongoClient.connect();
    
    mongoDB = mongoClient.db();
    
    // 测试连接
    await mongoDB.admin().ping();
    
    logger.info('MongoDB connection established successfully');
    return mongoDB;
  } catch (error) {
    logger.error('Failed to connect to MongoDB:', error);
    throw error;
  }
}

export async function connectRedis(): Promise<RedisClientType> {
  try {
    redisClient = createClient({
      url: config.redis.url,
      socket: {
        connectTimeout: 5000,
        lazyConnect: true
      }
    });

    redisClient.on('error', (error) => {
      logger.error('Redis error:', error);
    });

    redisClient.on('connect', () => {
      logger.info('Redis connected');
    });

    redisClient.on('disconnect', () => {
      logger.warn('Redis disconnected');
    });

    await redisClient.connect();
    await redisClient.ping();
    
    logger.info('Redis connection established successfully');
    return redisClient;
  } catch (error) {
    logger.error('Failed to connect to Redis:', error);
    throw error;
  }
}

export function getPostgreSQLPool(): Pool {
  if (!pgPool) {
    throw new Error('PostgreSQL pool not initialized. Call connectPostgreSQL() first.');
  }
  return pgPool;
}

export function getMongoDB(): Db {
  if (!mongoDB) {
    throw new Error('MongoDB not initialized. Call connectMongoDB() first.');
  }
  return mongoDB;
}

export function getRedisClient(): RedisClientType {
  if (!redisClient) {
    throw new Error('Redis client not initialized. Call connectRedis() first.');
  }
  return redisClient;
}

export async function pgQuery<T = any>(text: string, params?: any[]): Promise<T[]> {
  const client = await pgPool.connect();
  try {
    const start = Date.now();
    const result = await client.query(text, params);
    const duration = Date.now() - start;
    
    logger.debug('Executed PostgreSQL query', {
      text,
      duration,
      rows: result.rowCount
    });
    
    return result.rows;
  } catch (error) {
    logger.error('PostgreSQL query error:', { text, params, error });
    throw error;
  } finally {
    client.release();
  }
}

export async function pgTransaction<T>(
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  const client = await pgPool.connect();
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error('PostgreSQL transaction error:', error);
    throw error;
  } finally {
    client.release();
  }
}

export class CacheService {
  private client: RedisClientType;
  private prefix: string;
  private defaultTTL: number;

  constructor() {
    this.client = getRedisClient();
    this.prefix = config.redis.prefix;
    this.defaultTTL = config.redis.ttl;
  }

  private getKey(key: string): string {
    return `${this.prefix}${key}`;
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.client.get(this.getKey(key));
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error('Cache get error:', { key, error });
      return null;
    }
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<boolean> {
    try {
      const serialized = JSON.stringify(value);
      const expiration = ttl || this.defaultTTL;
      await this.client.setEx(this.getKey(key), expiration, serialized);
      return true;
    } catch (error) {
      logger.error('Cache set error:', { key, error });
      return false;
    }
  }

  async del(key: string): Promise<boolean> {
    try {
      await this.client.del(this.getKey(key));
      return true;
    } catch (error) {
      logger.error('Cache delete error:', { key, error });
      return false;
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(this.getKey(key));
      return result === 1;
    } catch (error) {
      logger.error('Cache exists error:', { key, error });
      return false;
    }
  }

  async increment(key: string, value: number = 1): Promise<number> {
    try {
      return await this.client.incrBy(this.getKey(key), value);
    } catch (error) {
      logger.error('Cache increment error:', { key, error });
      throw error;
    }
  }

  async expire(key: string, ttl: number): Promise<boolean> {
    try {
      await this.client.expire(this.getKey(key), ttl);
      return true;
    } catch (error) {
      logger.error('Cache expire error:', { key, error });
      return false;
    }
  }

  async flushPattern(pattern: string): Promise<void> {
    try {
      const keys = await this.client.keys(this.getKey(pattern));
      if (keys.length > 0) {
        await this.client.del(keys);
      }
    } catch (error) {
      logger.error('Cache flush pattern error:', { pattern, error });
    }
  }
}

export const cacheService = new CacheService();

export async function closeDatabases(): Promise<void> {
  const promises = [];

  if (pgPool) {
    promises.push(pgPool.end().then(() => logger.info('PostgreSQL connection closed')));
  }

  if (mongoClient) {
    promises.push(mongoClient.close().then(() => logger.info('MongoDB connection closed')));
  }

  if (redisClient) {
    promises.push(redisClient.quit().then(() => logger.info('Redis connection closed')));
  }

  await Promise.all(promises);
}
